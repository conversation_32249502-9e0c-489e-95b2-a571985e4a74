const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testMultiTenantFlow() {
  console.log('🧪 Testing Recovery Connect Multi-Tenant Organization Management\n');
  
  let adminToken = '';
  let memberToken = '';
  let organizationId = '';
  let memberId = '';

  try {
    const timestamp = Date.now().toString().slice(-6);

    // Step 1: Create admin user and organization
    console.log('1️⃣ Creating admin user and organization...');
    const adminEmail = `admin${timestamp}@testorg.com`;
    const adminRegister = await axios.post(`${API_BASE}/auth/register`, {
      email: adminEmail,
      username: `admin${timestamp}`,
      password: 'admin123',
      firstName: 'Admin',
      lastName: 'User'
    });

    const adminLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: adminEmail,
      password: 'admin123'
    });
    adminToken = adminLogin.data.access_token;

    const orgResponse = await axios.post(`${API_BASE}/organizations`, {
      name: 'Multi-Tenant Test Org',
      description: 'Testing multi-tenant functionality',
      address: '123 Test St, Test City, TC 12345',
      phone: '(*************',
      email: '<EMAIL>'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });

    organizationId = orgResponse.data.organization.id;
    adminToken = orgResponse.data.access_token; // Updated token with admin role
    console.log('✅ Admin user and organization created');

    // Step 2: Create a regular user (individual)
    console.log('\n2️⃣ Creating individual user...');
    const memberEmail = `member${timestamp}@example.com`;
    await axios.post(`${API_BASE}/auth/register`, {
      email: memberEmail,
      username: `member${timestamp}`,
      password: 'member123',
      firstName: 'Member',
      lastName: 'User'
    });

    const memberLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: memberEmail,
      password: 'member123'
    });
    memberToken = memberLogin.data.access_token;
    memberId = memberLogin.data.user.id;
    console.log('✅ Individual user created');

    // Step 3: Test that member cannot access org resources
    console.log('\n3️⃣ Testing access control before invitation...');
    try {
      await axios.get(`${API_BASE}/houses`, {
        headers: { Authorization: `Bearer ${memberToken}` }
      });
      console.log('❌ Access control failed - member can access houses without organization');
    } catch (error) {
      console.log('✅ Access control working - member cannot access houses without organization');
    }

    // Step 4: Admin invites user to organization
    console.log('\n4️⃣ Admin inviting user to organization...');
    const inviteResponse = await axios.post(`${API_BASE}/organizations/${organizationId}/invite`, {
      email: memberEmail,
      role: 'MEMBER'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ User invited successfully:', inviteResponse.data.email);

    // Step 5: Member logs in again to get updated token
    console.log('\n5️⃣ Member logging in with updated role...');
    const memberLoginUpdated = await axios.post(`${API_BASE}/auth/login`, {
      email: memberEmail,
      password: 'member123'
    });
    memberToken = memberLoginUpdated.data.access_token;
    console.log('✅ Member role:', memberLoginUpdated.data.user.role);
    console.log('✅ Member organization:', memberLoginUpdated.data.user.organizationId);

    // Step 6: Test that member can now access org resources
    console.log('\n6️⃣ Testing member access after invitation...');
    const housesResponse = await axios.get(`${API_BASE}/houses`, {
      headers: { Authorization: `Bearer ${memberToken}` }
    });
    console.log('✅ Member can now access houses:', housesResponse.data.length, 'houses found');

    // Step 7: Test member cannot perform admin actions
    console.log('\n7️⃣ Testing admin-only actions...');
    try {
      await axios.post(`${API_BASE}/houses`, {
        name: 'Unauthorized House',
        address: '456 Unauthorized St',
        capacity: 10
      }, {
        headers: { Authorization: `Bearer ${memberToken}` }
      });
      console.log('❌ Authorization failed - member can create houses');
    } catch (error) {
      console.log('✅ Authorization working - member cannot create houses');
    }

    // Step 8: Admin creates houses
    console.log('\n8️⃣ Admin creating houses...');
    await axios.post(`${API_BASE}/houses`, {
      name: 'Admin House 1',
      address: '789 Admin St',
      capacity: 12
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Admin successfully created house');

    // Step 9: Test organization member management
    console.log('\n9️⃣ Testing organization member management...');
    const orgDetails = await axios.get(`${API_BASE}/organizations/${organizationId}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Organization has', orgDetails.data.users.length, 'members');

    // Step 10: Admin removes member from organization
    console.log('\n🔟 Admin removing member from organization...');
    await axios.delete(`${API_BASE}/organizations/${organizationId}/remove/${memberId}`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Member removed from organization');

    // Step 11: Test that removed member loses access
    console.log('\n1️⃣1️⃣ Testing access after removal...');
    try {
      await axios.get(`${API_BASE}/houses`, {
        headers: { Authorization: `Bearer ${memberToken}` }
      });
      console.log('❌ Access control failed - removed member can still access houses');
    } catch (error) {
      console.log('✅ Access control working - removed member cannot access houses');
    }

    // Step 12: Verify member is back to individual status
    console.log('\n1️⃣2️⃣ Verifying member status after removal...');
    const memberProfile = await axios.get(`${API_BASE}/auth/profile`, {
      headers: { Authorization: `Bearer ${memberToken}` }
    });
    console.log('✅ Member role after removal:', memberProfile.data.role);
    console.log('✅ Member organization after removal:', memberProfile.data.organizationId || 'None');

    console.log('\n🎉 All multi-tenant tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`- Organization created: ${organizationId}`);
    console.log('- User invitation: ✅ Working');
    console.log('- Role-based access control: ✅ Working');
    console.log('- User removal: ✅ Working');
    console.log('- Data isolation: ✅ Working');
    console.log('- Personal data preservation: ✅ Working');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testMultiTenantFlow();
