const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding sample data...');

  try {
    // Create sample meetings
    const meetings = await Promise.all([
      prisma.meeting.create({
        data: {
          title: 'Downtown AA Meeting',
          description: 'Open discussion meeting for anyone seeking recovery support',
          locationName: 'Community Center',
          address: '123 Main St, Downtown',
          latitude: 40.7128,
          longitude: -74.0060,
          startTime: new Date('2025-06-29T19:00:00Z'),
          endTime: new Date('2025-06-29T20:00:00Z'),
          meetingType: 'AA',
          isRecurring: true,
          recurrencePattern: 'weekly',
          contactInfo: '<EMAIL>',
        },
      }),
      prisma.meeting.create({
        data: {
          title: 'Narcotics Anonymous Step Study',
          description: 'Working through the 12 steps together',
          locationName: 'Unity Church',
          address: '456 Oak Ave, Midtown',
          latitude: 40.7589,
          longitude: -73.9851,
          startTime: new Date('2025-06-30T18:30:00Z'),
          endTime: new Date('2025-06-30T19:30:00Z'),
          meetingType: 'NA',
          isRecurring: true,
          recurrencePattern: 'weekly',
          contactInfo: '<EMAIL>',
        },
      }),
      prisma.meeting.create({
        data: {
          title: 'Young People in Recovery',
          description: 'Support group for people under 30 in recovery',
          locationName: 'Youth Center',
          address: '789 Pine St, Uptown',
          latitude: 40.7831,
          longitude: -73.9712,
          startTime: new Date('2025-07-01T20:00:00Z'),
          endTime: new Date('2025-07-01T21:00:00Z'),
          meetingType: 'AA',
          isRecurring: true,
          recurrencePattern: 'weekly',
          contactInfo: '<EMAIL>',
        },
      }),
    ]);

    console.log(`✅ Created ${meetings.length} sample meetings`);

    // Get the first user to create sample posts
    const firstUser = await prisma.user.findFirst();
    
    if (firstUser) {
      // Create sample social posts
      const posts = await Promise.all([
        prisma.post.create({
          data: {
            content: 'Just celebrated 30 days clean! Feeling grateful for this community and all the support. One day at a time! 🙏',
            postType: 'TEXT',
            isAnonymous: false,
            userId: firstUser.id,
          },
        }),
        prisma.post.create({
          data: {
            content: 'Having a tough day today. The cravings are strong but I know I can get through this. Thank you all for being here.',
            postType: 'TEXT',
            isAnonymous: true,
            userId: firstUser.id,
          },
        }),
        prisma.post.create({
          data: {
            content: 'Attended my first meeting today and it was amazing. So many inspiring stories and such a welcoming community. Looking forward to the journey ahead!',
            postType: 'TEXT',
            isAnonymous: false,
            userId: firstUser.id,
          },
        }),
      ]);

      console.log(`✅ Created ${posts.length} sample posts`);

      // Create sample reactions
      const reactions = await Promise.all([
        prisma.reaction.create({
          data: {
            postId: posts[0].id,
            userId: firstUser.id,
            reactionType: 'CELEBRATE',
          },
        }),
        prisma.reaction.create({
          data: {
            postId: posts[1].id,
            userId: firstUser.id,
            reactionType: 'SUPPORT',
          },
        }),
        prisma.reaction.create({
          data: {
            postId: posts[2].id,
            userId: firstUser.id,
            reactionType: 'HEART',
          },
        }),
      ]);

      console.log(`✅ Created ${reactions.length} sample reactions`);

      // Create sample meeting ratings
      const ratings = await Promise.all([
        prisma.meetingRating.create({
          data: {
            meetingId: meetings[0].id,
            userId: firstUser.id,
            rating: 5,
            review: 'Great meeting with very supportive people. Highly recommend!',
          },
        }),
        prisma.meetingRating.create({
          data: {
            meetingId: meetings[1].id,
            userId: firstUser.id,
            rating: 4,
            review: 'Good step study format, learned a lot.',
          },
        }),
      ]);

      console.log(`✅ Created ${ratings.length} sample ratings`);

      // Create sample chat room
      const chatRoom = await prisma.chatRoom.create({
        data: {
          name: 'General Support',
          roomType: 'TOPIC',
          isPrivate: false,
          createdBy: firstUser.id,
        },
      });

      // Create sample messages
      const messages = await Promise.all([
        prisma.message.create({
          data: {
            roomId: chatRoom.id,
            userId: firstUser.id,
            content: 'Welcome to the general support chat! Feel free to share and ask questions.',
            messageType: 'TEXT',
          },
        }),
        prisma.message.create({
          data: {
            roomId: chatRoom.id,
            userId: firstUser.id,
            content: 'Remember, we\'re all here to support each other on this journey.',
            messageType: 'TEXT',
          },
        }),
      ]);

      console.log(`✅ Created chat room with ${messages.length} sample messages`);

      // Create sample journal entry linked to a meeting
      const journalEntry = await prisma.journal.create({
        data: {
          title: 'Reflections after tonight\'s meeting',
          encryptedContent: 'U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y=', // Encrypted "Tonight's meeting was really powerful. I felt connected to the group and shared my story for the first time."
          mood: 8,
          tags: 'meeting,sharing,progress',
          meetingId: meetings[0].id,
          userId: firstUser.id,
        },
      });

      console.log(`✅ Created sample journal entry linked to meeting`);
    }

    console.log('🎉 Sample data seeding completed successfully!');
    console.log('\nYou can now test the new features:');
    console.log('- Visit /meetings to see nearby meetings');
    console.log('- Visit /social to see the community feed');
    console.log('- Check the journal for meeting-linked notes');
    console.log('- Test the rating and reaction systems');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
