// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Recovery-specific fields
  sobrietyDate DateTime?
  timezone     String   @default("UTC")

  // Multi-tenant organization
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  role           String        @default("INDIVIDUAL") // INDIVIDUAL, MEMBER, STAFF, ADMIN

  // Relations
  journals         Journal[]
  events           Event[]
  panicContacts    PanicContact[]
  cleanTime        CleanTime[]
  quotes           Quote[]
  gratitudeEntries GratitudeEntry[]

  @@map("users")
}

model Journal {
  id              String   @id @default(cuid())
  title           String
  encryptedContent String  // AES-256 encrypted content
  mood            Int?     // 1-10 scale
  tags            String?  // Comma-separated tags
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("journals")
}

model Event {
  id          String    @id @default(cuid())
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime?
  location    String?
  type        String    @default("APPOINTMENT")
  isRecurring Boolean   @default(false)
  recurrence  String?   // JSON string for recurrence rules
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("events")
}

model PanicContact {
  id          String  @id @default(cuid())
  name        String
  phoneNumber String
  relationship String?
  isPrimary   Boolean @default(false)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("panic_contacts")
}

model CleanTime {
  id        String   @id @default(cuid())
  startDate DateTime
  endDate   DateTime?
  isActive  Boolean  @default(true)
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("clean_time")
}

// EventType values: MEETING, APPOINTMENT, THERAPY, SUPPORT_GROUP, PERSONAL, REMINDER

model Organization {
  id          String   @id @default(cuid())
  name        String
  description String?
  address     String?
  phone       String?
  email       String?
  website     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users   User[]
  houses  House[]

  @@map("organizations")
}

model House {
  id          String   @id @default(cuid())
  name        String
  address     String
  capacity    Int?
  notes       String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  maintenanceRequests MaintenanceRequest[]

  @@map("houses")
}

model MaintenanceRequest {
  id          String   @id @default(cuid())
  title       String
  description String?
  priority    String   @default("medium") // low, medium, high
  status      String   @default("pending") // pending, in_progress, completed, cancelled
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  houseId String
  house   House  @relation(fields: [houseId], references: [id], onDelete: Cascade)

  @@map("maintenance_requests")
}

model Quote {
  id        String   @id @default(cuid())
  text      String
  tags      String?  // Comma-separated tags
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("quotes")
}

model GratitudeEntry {
  id        String   @id @default(cuid())
  text      String
  createdAt DateTime @default(now())

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("gratitude_entries")
}
