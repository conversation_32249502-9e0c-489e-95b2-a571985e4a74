// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Recovery-specific fields
  sobrietyDate DateTime?
  timezone     String   @default("UTC")

  // Multi-tenant organization
  organizationId String?
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: SetNull)
  role           String        @default("INDIVIDUAL") // INDIVIDUAL, MEMBER, STAFF, ADMIN

  // Relations
  journals         Journal[]
  events           Event[]
  panicContacts    PanicContact[]
  cleanTime        CleanTime[]
  quotes           Quote[]
  gratitudeEntries GratitudeEntry[]

  // New relations for extended features
  meetingRatings   MeetingRating[]
  posts            Post[]
  reactions        Reaction[]
  createdChatRooms ChatRoom[]
  messages         Message[]
  reports          Report[] @relation("UserReports")
  reportedBy       Report[] @relation("UserReported")
  moderatedReports Report[] @relation("UserModerated")

  @@map("users")
}

model Journal {
  id              String   @id @default(cuid())
  title           String
  encryptedContent String  // AES-256 encrypted content
  mood            Int?     // 1-10 scale
  tags            String?  // Comma-separated tags
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Optional meeting association for private notes
  meetingId String?  @map("meeting_id")
  meeting   Meeting? @relation(fields: [meetingId], references: [id], onDelete: SetNull)

  @@map("journals")
}

model Event {
  id          String    @id @default(cuid())
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime?
  location    String?
  type        String    @default("APPOINTMENT")
  isRecurring Boolean   @default(false)
  recurrence  String?   // JSON string for recurrence rules
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("events")
}

model PanicContact {
  id          String  @id @default(cuid())
  name        String
  phoneNumber String
  relationship String?
  isPrimary   Boolean @default(false)
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("panic_contacts")
}

model CleanTime {
  id        String   @id @default(cuid())
  startDate DateTime
  endDate   DateTime?
  isActive  Boolean  @default(true)
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("clean_time")
}

// EventType values: MEETING, APPOINTMENT, THERAPY, SUPPORT_GROUP, PERSONAL, REMINDER

model Organization {
  id          String   @id @default(cuid())
  name        String
  description String?
  address     String?
  phone       String?
  email       String?
  website     String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  users   User[]
  houses  House[]

  @@map("organizations")
}

model House {
  id          String   @id @default(cuid())
  name        String
  address     String
  capacity    Int?
  notes       String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  maintenanceRequests MaintenanceRequest[]

  @@map("houses")
}

model MaintenanceRequest {
  id          String   @id @default(cuid())
  title       String
  description String?
  priority    String   @default("medium") // low, medium, high
  status      String   @default("pending") // pending, in_progress, completed, cancelled
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  houseId String
  house   House  @relation(fields: [houseId], references: [id], onDelete: Cascade)

  @@map("maintenance_requests")
}

model Quote {
  id        String   @id @default(cuid())
  text      String
  tags      String?  // Comma-separated tags
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("quotes")
}

model GratitudeEntry {
  id        String   @id @default(cuid())
  text      String
  createdAt DateTime @default(now())

  // Relations
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("gratitude_entries")
}

// New tables for extended features

model Meeting {
  id                String   @id @default(cuid())
  title             String
  description       String?
  locationName      String?  @map("location_name")
  address           String?
  latitude          Float?   // Changed from Decimal to Float for SQLite
  longitude         Float?   // Changed from Decimal to Float for SQLite
  startTime         DateTime @map("start_time")
  endTime           DateTime? @map("end_time")
  meetingType       String   @default("AA") @map("meeting_type")
  isRecurring       Boolean  @default(false) @map("is_recurring")
  recurrencePattern String?  @map("recurrence_pattern")
  contactInfo       String?  @map("contact_info")
  isActive          Boolean  @default(true) @map("is_active")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  ratings    MeetingRating[]
  chatRooms  ChatRoom[]
  journals   Journal[]

  @@map("meetings")
}

model MeetingRating {
  id        String   @id @default(cuid())
  rating    Int      // Removed @db.SmallInt for SQLite compatibility
  review    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  meetingId String  @map("meeting_id")
  meeting   Meeting @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  userId    String  @map("user_id")
  user      User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([meetingId, userId])
  @@map("meeting_ratings")
}

model Post {
  id          String   @id @default(cuid())
  content     String
  mediaUrls   String?  @map("media_urls") // Changed to String (JSON) for SQLite compatibility
  postType    String   @default("TEXT") @map("post_type")
  isAnonymous Boolean  @default(false) @map("is_anonymous")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userId    String     @map("user_id")
  user      User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  reactions Reaction[]
  reports   Report[]

  @@map("posts")
}

model Reaction {
  id           String   @id @default(cuid())
  reactionType String   @map("reaction_type")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  postId String @map("post_id")
  post   Post   @relation(fields: [postId], references: [id], onDelete: Cascade)
  userId String @map("user_id")
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([postId, userId, reactionType])
  @@map("reactions")
}

model ChatRoom {
  id        String   @id @default(cuid())
  name      String
  roomType  String   @map("room_type")
  isPrivate Boolean  @default(false) @map("is_private")
  createdAt DateTime @default(now()) @map("created_at")

  // Relations
  meetingId String?   @map("meeting_id")
  meeting   Meeting?  @relation(fields: [meetingId], references: [id], onDelete: Cascade)
  createdBy String    @map("created_by")
  creator   User      @relation(fields: [createdBy], references: [id])
  messages  Message[]

  @@map("chat_rooms")
}

model Message {
  id          String   @id @default(cuid())
  content     String
  messageType String   @default("TEXT") @map("message_type")
  isEdited    Boolean  @default(false) @map("is_edited")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  roomId String   @map("room_id")
  room   ChatRoom @relation(fields: [roomId], references: [id], onDelete: Cascade)
  userId String   @map("user_id")
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  reports Report[]

  @@map("messages")
}

model Report {
  id             String    @id @default(cuid())
  contentType    String    @map("content_type")
  contentId      String    @map("content_id")
  reason         String
  description    String?
  status         String    @default("PENDING")
  moderatorNotes String?   @map("moderator_notes")
  createdAt      DateTime  @default(now()) @map("created_at")
  resolvedAt     DateTime? @map("resolved_at")

  // Relations
  reporterId     String  @map("reporter_id")
  reporter       User    @relation("UserReports", fields: [reporterId], references: [id])
  reportedUserId String? @map("reported_user_id")
  reportedUser   User?   @relation("UserReported", fields: [reportedUserId], references: [id])
  moderatorId    String? @map("moderator_id")
  moderator      User?   @relation("UserModerated", fields: [moderatorId], references: [id])

  // Optional relations to specific content types
  postId    String?  @map("post_id")
  post      Post?    @relation(fields: [postId], references: [id], onDelete: Cascade)
  messageId String?  @map("message_id")
  message   Message? @relation(fields: [messageId], references: [id], onDelete: Cascade)

  @@map("reports")
}
