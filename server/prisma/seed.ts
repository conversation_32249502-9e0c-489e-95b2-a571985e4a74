import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Check if organization already exists
  let organization = await prisma.organization.findFirst({
    where: { name: 'Hope Recovery Center' }
  });

  if (!organization) {
    // Create demo organization
    organization = await prisma.organization.create({
      data: {
        name: 'Hope Recovery Center',
        description: 'A comprehensive recovery center providing support and housing',
        address: '123 Recovery Street, Hope City, HC 12345',
        phone: '(*************',
        email: '<EMAIL>',
        website: 'https://hoperecovery.org',
      },
    });
    console.log('✅ Created organization:', organization.name);
  } else {
    console.log('✅ Organization already exists:', organization.name);
  }

  // Create or find admin user
  let adminUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!adminUser) {
    const adminPassword = await bcrypt.hash('admin123', 12);
    adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'admin',
        password: adminPassword,
        firstName: 'Admin',
        lastName: 'User',
        organizationId: organization.id,
        role: 'ADMIN',
        sobrietyDate: new Date('2020-01-01'),
      },
    });
    console.log('✅ Created admin user:', adminUser.email);
  } else {
    console.log('✅ Admin user already exists:', adminUser.email);
  }

  // Create or find staff user
  let staffUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!staffUser) {
    const staffPassword = await bcrypt.hash('staff123', 12);
    staffUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'staff',
        password: staffPassword,
        firstName: 'Staff',
        lastName: 'Member',
        organizationId: organization.id,
        role: 'STAFF',
        sobrietyDate: new Date('2021-06-15'),
      },
    });
    console.log('✅ Created staff user:', staffUser.email);
  } else {
    console.log('✅ Staff user already exists:', staffUser.email);
  }

  // Create or find member users
  let member1 = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!member1) {
    const memberPassword = await bcrypt.hash('member123', 12);
    member1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'john_doe',
        password: memberPassword,
        firstName: 'John',
        lastName: 'Doe',
        organizationId: organization.id,
        role: 'MEMBER',
        sobrietyDate: new Date('2023-03-10'),
      },
    });
    console.log('✅ Created member user 1:', member1.email);
  } else {
    console.log('✅ Member user 1 already exists:', member1.email);
  }

  let member2 = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!member2) {
    const memberPassword = await bcrypt.hash('member123', 12);
    member2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'jane_smith',
        password: memberPassword,
        firstName: 'Jane',
        lastName: 'Smith',
        organizationId: organization.id,
        role: 'MEMBER',
        sobrietyDate: new Date('2023-08-22'),
      },
    });
    console.log('✅ Created member user 2:', member2.email);
  } else {
    console.log('✅ Member user 2 already exists:', member2.email);
  }

  // Create or find individual user (not part of organization)
  let individualUser = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  });

  if (!individualUser) {
    const individualPassword = await bcrypt.hash('individual123', 12);
    individualUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'individual',
        password: individualPassword,
        firstName: 'Individual',
        lastName: 'User',
        role: 'INDIVIDUAL',
        sobrietyDate: new Date('2022-12-01'),
      },
    });
    console.log('✅ Created individual user:', individualUser.email);
  } else {
    console.log('✅ Individual user already exists:', individualUser.email);
  }

  // Create houses
  const house1 = await prisma.house.create({
    data: {
      name: 'Main House',
      address: '456 Recovery Avenue, Hope City, HC 12345',
      capacity: 12,
      notes: 'Primary residential facility with 12 beds',
      organizationId: organization.id,
    },
  });

  const house2 = await prisma.house.create({
    data: {
      name: 'Transitional House',
      address: '789 Hope Boulevard, Hope City, HC 12345',
      capacity: 8,
      notes: 'For residents transitioning to independent living',
      organizationId: organization.id,
    },
  });

  console.log('✅ Created houses');

  // Create maintenance requests
  await prisma.maintenanceRequest.create({
    data: {
      title: 'Fix leaky faucet in kitchen',
      description: 'The kitchen faucet in the main house has been dripping for several days',
      priority: 'medium',
      status: 'pending',
      houseId: house1.id,
      notes: 'Located in main kitchen area, affects water bill',
    },
  });

  await prisma.maintenanceRequest.create({
    data: {
      title: 'Replace broken window',
      description: 'Window in bedroom 3 is cracked and needs immediate replacement',
      priority: 'high',
      status: 'in_progress',
      houseId: house1.id,
      notes: 'Safety concern - cold air coming through crack',
    },
  });

  await prisma.maintenanceRequest.create({
    data: {
      title: 'Paint common area',
      description: 'Common area walls need fresh paint',
      priority: 'low',
      status: 'pending',
      houseId: house2.id,
      notes: 'Cosmetic improvement for resident morale',
    },
  });

  console.log('✅ Created maintenance requests');

  // Create some sample journal entries for the individual user
  await prisma.journal.create({
    data: {
      title: 'Day 1 of Recovery',
      encryptedContent: 'U2FsdGVkX1+vupppZksvRf5pq5g5XjFRIipRkwB0K1Y=', // Encrypted "Today marks the beginning of my recovery journey"
      mood: 7,
      tags: 'recovery,hope,beginning',
      userId: individualUser.id,
    },
  });

  // Create sample quotes for different users (only if they don't exist)
  const existingQuotes = await prisma.quote.count();
  if (existingQuotes === 0) {
    await prisma.quote.createMany({
      data: [
        {
          text: 'One day at a time, one step at a time.',
          tags: 'recovery,daily,motivation',
          userId: adminUser.id,
        },
        {
          text: 'Progress, not perfection.',
          tags: 'progress,mindset,growth',
          userId: adminUser.id,
        },
        {
          text: 'You are stronger than you think.',
          tags: 'strength,self-belief,courage',
          userId: member1.id,
        },
        {
          text: 'Every setback is a setup for a comeback.',
          tags: 'resilience,hope,recovery',
          userId: member2.id,
        },
        {
          text: 'The only way out is through.',
          tags: 'perseverance,courage,journey',
          userId: individualUser.id,
        },
      ],
    });
    console.log('✅ Created sample quotes');
  } else {
    console.log('✅ Sample quotes already exist');
  }

  // Create sample gratitude entries (only if they don't exist)
  const existingGratitude = await prisma.gratitudeEntry.count();
  if (existingGratitude === 0) {
    await prisma.gratitudeEntry.createMany({
      data: [
        {
          text: 'I am grateful for my family\'s unwavering support.',
          userId: adminUser.id,
        },
        {
          text: 'I am grateful for having a safe place to call home.',
          userId: adminUser.id,
        },
        {
          text: 'I am grateful for the strength to face each new day.',
          userId: member1.id,
        },
        {
          text: 'I am grateful for the friends who believe in my recovery.',
          userId: member1.id,
        },
        {
          text: 'I am grateful for second chances.',
          userId: member2.id,
        },
        {
          text: 'I am grateful for the opportunity to help others.',
          userId: individualUser.id,
        },
      ],
    });
    console.log('✅ Created sample gratitude entries');
  } else {
    console.log('✅ Sample gratitude entries already exist');
  }
  console.log('✅ Created sample data');

  console.log('\n🎉 Seed completed successfully!');
  console.log('\n📋 Demo Accounts:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Staff: <EMAIL> / staff123');
  console.log('Member: <EMAIL> / member123');
  console.log('Member: <EMAIL> / member123');
  console.log('Individual: <EMAIL> / individual123');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
