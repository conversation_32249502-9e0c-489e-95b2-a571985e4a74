import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/modules/app.module';
import { PrismaService } from '../src/modules/prisma/prisma.service';

describe('Multi-Tenant Organization Management (e2e)', () => {
  let app: INestApplication;
  let prisma: PrismaService;
  let adminToken: string;
  let memberToken: string;
  let organizationId: string;
  let memberId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    prisma = moduleFixture.get<PrismaService>(PrismaService);
    await app.init();

    // Clean up test data
    await prisma.maintenanceRequest.deleteMany();
    await prisma.house.deleteMany();
    await prisma.user.deleteMany();
    await prisma.organization.deleteMany();
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.maintenanceRequest.deleteMany();
    await prisma.house.deleteMany();
    await prisma.user.deleteMany();
    await prisma.organization.deleteMany();
    await app.close();
  });

  describe('Organization Creation and Admin Role', () => {
    it('should register admin user', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'admin_test',
          password: 'test123',
          firstName: 'Admin',
          lastName: 'User',
        })
        .expect(201);

      expect(response.body.user.role).toBe('INDIVIDUAL');
      expect(response.body.user.organizationId).toBeNull();
      adminToken = response.body.access_token;
    });

    it('should create organization and make user admin', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/organizations')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test Recovery Center',
          description: 'A test organization',
          address: '123 Test St',
          phone: '(*************',
          email: '<EMAIL>',
        })
        .expect(201);

      organizationId = response.body.organization.id;
      adminToken = response.body.access_token; // Updated token with admin role
      expect(response.body.organization.name).toBe('Test Recovery Center');
      expect(response.body.access_token).toBeDefined();
    });

    it('should verify admin role and organization membership', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.role).toBe('ADMIN');
      expect(response.body.organizationId).toBe(organizationId);
    });
  });

  describe('User Invitation and Role Management', () => {
    it('should register member user', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'member_test',
          password: 'test123',
          firstName: 'Member',
          lastName: 'User',
        })
        .expect(201);

      memberId = response.body.user.id;
      memberToken = response.body.access_token;
      expect(response.body.user.role).toBe('INDIVIDUAL');
    });

    it('should prevent member from accessing organization resources', async () => {
      await request(app.getHttpServer())
        .get('/api/houses')
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(403);
    });

    it('should allow admin to invite member to organization', async () => {
      const response = await request(app.getHttpServer())
        .post(`/api/organizations/${organizationId}/invite`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          email: '<EMAIL>',
          role: 'MEMBER',
        })
        .expect(201);

      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.role).toBe('MEMBER');
    });

    it('should update member token after invitation', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'test123',
        })
        .expect(201);

      memberToken = response.body.access_token;
      expect(response.body.user.role).toBe('MEMBER');
      expect(response.body.user.organizationId).toBe(organizationId);
    });

    it('should allow member to access organization resources', async () => {
      await request(app.getHttpServer())
        .get('/api/houses')
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(200);
    });
  });

  describe('Role-Based Access Control', () => {
    it('should allow admin to create houses', async () => {
      const response = await request(app.getHttpServer())
        .post('/api/houses')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'Test House',
          address: '456 Test Ave',
          capacity: 10,
        })
        .expect(201);

      expect(response.body.name).toBe('Test House');
    });

    it('should prevent member from creating houses', async () => {
      await request(app.getHttpServer())
        .post('/api/houses')
        .set('Authorization', `Bearer ${memberToken}`)
        .send({
          name: 'Unauthorized House',
          address: '789 Unauthorized St',
          capacity: 5,
        })
        .expect(403);
    });

    it('should allow member to view houses', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/houses')
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(200);

      expect(response.body.length).toBeGreaterThan(0);
    });
  });

  describe('Organization Member Management', () => {
    it('should allow admin to view organization members', async () => {
      const response = await request(app.getHttpServer())
        .get(`/api/organizations/${organizationId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.users.length).toBe(2); // Admin + Member
    });

    it('should prevent member from accessing organization details', async () => {
      await request(app.getHttpServer())
        .get(`/api/organizations/${organizationId}`)
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(200); // Members can view their own org
    });

    it('should allow admin to remove member from organization', async () => {
      await request(app.getHttpServer())
        .delete(`/api/organizations/${organizationId}/remove/${memberId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
    });

    it('should revert member to individual status after removal', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(200);

      expect(response.body.role).toBe('INDIVIDUAL');
      expect(response.body.organizationId).toBeNull();
    });

    it('should prevent removed member from accessing organization resources', async () => {
      await request(app.getHttpServer())
        .get('/api/houses')
        .set('Authorization', `Bearer ${memberToken}`)
        .expect(403);
    });
  });

  describe('Data Isolation', () => {
    let admin2Token: string;
    let organization2Id: string;

    it('should create second organization with different admin', async () => {
      // Register second admin
      const registerResponse = await request(app.getHttpServer())
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          username: 'admin2_test',
          password: 'test123',
          firstName: 'Admin2',
          lastName: 'User',
        })
        .expect(201);

      admin2Token = registerResponse.body.access_token;

      // Create second organization
      const orgResponse = await request(app.getHttpServer())
        .post('/api/organizations')
        .set('Authorization', `Bearer ${admin2Token}`)
        .send({
          name: 'Second Recovery Center',
          description: 'Another test organization',
        })
        .expect(201);

      organization2Id = orgResponse.body.organization.id;
      admin2Token = orgResponse.body.access_token;
    });

    it('should ensure organizations cannot see each others data', async () => {
      // Admin1 should not see Admin2's organization
      const admin1Orgs = await request(app.getHttpServer())
        .get('/api/organizations/my-organization')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(admin1Orgs.body.id).toBe(organizationId);

      // Admin2 should not see Admin1's organization
      const admin2Orgs = await request(app.getHttpServer())
        .get('/api/organizations/my-organization')
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(200);

      expect(admin2Orgs.body.id).toBe(organization2Id);
      expect(admin2Orgs.body.id).not.toBe(organizationId);
    });
  });
});
