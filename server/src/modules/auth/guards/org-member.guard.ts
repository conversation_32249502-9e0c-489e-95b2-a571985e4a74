import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';

@Injectable()
export class OrgMemberGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      throw new ForbiddenException('User not authenticated');
    }

    if (!user.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    // Allow MEMBER, STAFF, or ADMIN roles
    const allowedRoles = ['MEMBER', 'STAFF', 'ADMIN'];
    if (!allowedRoles.includes(user.role)) {
      throw new ForbiddenException('Organization membership required');
    }

    return true;
  }
}
