import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { OrganizationsService } from './organizations.service';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('organizations')
@UseGuards(JwtAuthGuard)
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  @Post()
  create(@Body() createOrganizationDto: CreateOrganizationDto, @Request() req) {
    return this.organizationsService.create(createOrganizationDto, req.user.userId);
  }

  @Get('my-organization')
  getMyOrganization(@Request() req) {
    return this.organizationsService.getUserOrganization(req.user.userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.organizationsService.findOne(id, req.user.userId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateOrganizationDto: UpdateOrganizationDto, @Request() req) {
    return this.organizationsService.update(id, updateOrganizationDto, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.organizationsService.remove(id, req.user.userId);
  }

  @Post(':id/invite')
  inviteUser(
    @Param('id') organizationId: string,
    @Body() inviteDto: { email: string; role: string },
    @Request() req
  ) {
    return this.organizationsService.inviteUser(
      organizationId,
      inviteDto.email,
      inviteDto.role,
      req.user.userId
    );
  }

  @Delete(':id/remove/:userId')
  removeUser(
    @Param('id') organizationId: string,
    @Param('userId') userIdToRemove: string,
    @Request() req
  ) {
    return this.organizationsService.removeUser(
      organizationId,
      userIdToRemove,
      req.user.userId
    );
  }
}
