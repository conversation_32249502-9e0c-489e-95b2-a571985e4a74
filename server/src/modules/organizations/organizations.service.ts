import { Injectable, NotFoundException, ForbiddenException, ConflictException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';

@Injectable()
export class OrganizationsService {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
  ) {}

  async create(createOrganizationDto: CreateOrganizationDto, userId: string) {
    // Create organization and set the creator as admin
    const organization = await this.prisma.organization.create({
      data: createOrganizationDto,
      include: {
        users: true,
        houses: true,
      },
    });

    // Update user to be admin of this organization
    const updatedUser = await this.prisma.user.update({
      where: { id: userId },
      data: {
        organizationId: organization.id,
        role: 'ADMI<PERSON>',
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        organizationId: true,
        role: true,
      },
    });

    // Generate new JWT token with updated role and organization
    const payload = {
      email: updatedUser.email,
      sub: updatedUser.id,
      username: updatedUser.username,
      organizationId: updatedUser.organizationId,
      role: updatedUser.role,
    };

    return {
      organization,
      access_token: this.jwtService.sign(payload),
    };
  }

  async findAll() {
    return this.prisma.organization.findMany({
      where: { isActive: true },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
        houses: true,
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string, userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId || user.organizationId !== id) {
      throw new ForbiddenException('Access denied to this organization');
    }

    const organization = await this.prisma.organization.findUnique({
      where: { id },
      include: {
        users: {
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            role: true,
            createdAt: true,
          },
        },
        houses: {
          include: {
            maintenanceRequests: {
              select: {
                id: true,
                title: true,
                priority: true,
                status: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization;
  }

  async update(id: string, updateOrganizationDto: UpdateOrganizationDto, userId: string) {
    // Check if user is admin of this organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId || user.organizationId !== id || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can update organization details');
    }

    const organization = await this.prisma.organization.update({
      where: { id },
      data: updateOrganizationDto,
      include: {
        users: {
          select: {
            id: true,
            email: true,
            username: true,
            firstName: true,
            lastName: true,
            role: true,
          },
        },
        houses: true,
      },
    });

    return organization;
  }

  async remove(id: string, userId: string) {
    // Check if user is admin of this organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId || user.organizationId !== id || user.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can delete the organization');
    }

    // Soft delete by setting isActive to false
    return this.prisma.organization.update({
      where: { id },
      data: { isActive: false },
    });
  }

  async getUserOrganization(userId: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      include: {
        organization: {
          include: {
            houses: {
              include: {
                maintenanceRequests: {
                  where: { status: { not: 'completed' } },
                  orderBy: { createdAt: 'desc' },
                  take: 5,
                },
              },
            },
          },
        },
      },
    });

    return user?.organization || null;
  }

  async inviteUser(organizationId: string, userEmail: string, role: string, adminUserId: string) {
    // Check if admin user has permission
    const adminUser = await this.prisma.user.findUnique({
      where: { id: adminUserId },
      select: { organizationId: true, role: true },
    });

    if (!adminUser?.organizationId || adminUser.organizationId !== organizationId || adminUser.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can invite users');
    }

    // Find the user to invite
    const userToInvite = await this.prisma.user.findUnique({
      where: { email: userEmail },
    });

    if (!userToInvite) {
      throw new NotFoundException('User not found');
    }

    if (userToInvite.organizationId) {
      throw new ConflictException('User already belongs to an organization');
    }

    // Valid roles for organization members
    const validRoles = ['MEMBER', 'STAFF', 'ADMIN'];
    if (!validRoles.includes(role)) {
      throw new BadRequestException('Invalid role. Must be MEMBER, STAFF, or ADMIN');
    }

    // Add user to organization
    const updatedUser = await this.prisma.user.update({
      where: { id: userToInvite.id },
      data: {
        organizationId: organizationId,
        role: role as any,
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
        createdAt: true,
      },
    });

    return updatedUser;
  }

  async removeUser(organizationId: string, userIdToRemove: string, adminUserId: string) {
    // Check if admin user has permission
    const adminUser = await this.prisma.user.findUnique({
      where: { id: adminUserId },
      select: { organizationId: true, role: true },
    });

    if (!adminUser?.organizationId || adminUser.organizationId !== organizationId || adminUser.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can remove users');
    }

    // Find the user to remove
    const userToRemove = await this.prisma.user.findUnique({
      where: { id: userIdToRemove },
      select: { id: true, organizationId: true, role: true },
    });

    if (!userToRemove) {
      throw new NotFoundException('User not found');
    }

    if (userToRemove.organizationId !== organizationId) {
      throw new ForbiddenException('User does not belong to this organization');
    }

    if (userToRemove.id === adminUserId) {
      throw new ForbiddenException('Cannot remove yourself from the organization');
    }

    // Remove user from organization (revert to individual status)
    const updatedUser = await this.prisma.user.update({
      where: { id: userIdToRemove },
      data: {
        organizationId: null,
        role: 'INDIVIDUAL',
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        role: true,
      },
    });

    return updatedUser;
  }
}
