import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';

@Injectable()
export class EventsService {
  constructor(private prisma: PrismaService) {}

  async create(createEventDto: CreateEventDto, userId: string) {
    return this.prisma.event.create({
      data: {
        ...createEventDto,
        startTime: new Date(createEventDto.startTime),
        endTime: createEventDto.endTime ? new Date(createEventDto.endTime) : null,
        userId,
      },
    });
  }

  async findAll(userId: string, startDate?: string, endDate?: string) {
    const where: any = { userId };

    if (startDate || endDate) {
      where.startTime = {};
      if (startDate) {
        where.startTime.gte = new Date(startDate);
      }
      if (endDate) {
        where.startTime.lte = new Date(endDate);
      }
    }

    return this.prisma.event.findMany({
      where,
      orderBy: { startTime: 'asc' },
    });
  }

  async findOne(id: string, userId: string) {
    const event = await this.prisma.event.findUnique({
      where: { id },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    if (event.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return event;
  }

  async update(id: string, updateEventDto: UpdateEventDto, userId: string) {
    const event = await this.prisma.event.findUnique({
      where: { id },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    if (event.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return this.prisma.event.update({
      where: { id },
      data: {
        ...updateEventDto,
        startTime: updateEventDto.startTime ? new Date(updateEventDto.startTime) : undefined,
        endTime: updateEventDto.endTime ? new Date(updateEventDto.endTime) : undefined,
      },
    });
  }

  async remove(id: string, userId: string) {
    const event = await this.prisma.event.findUnique({
      where: { id },
    });

    if (!event) {
      throw new NotFoundException('Event not found');
    }

    if (event.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return this.prisma.event.delete({
      where: { id },
    });
  }

  async findUpcoming(userId: string, limit: number = 5) {
    return this.prisma.event.findMany({
      where: {
        userId,
        startTime: {
          gte: new Date(),
        },
      },
      orderBy: { startTime: 'asc' },
      take: limit,
    });
  }
}
