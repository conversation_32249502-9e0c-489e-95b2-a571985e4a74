import { IsString, IsOptional, IsDateString, IsBoolean, IsEnum } from 'class-validator';

export enum EventType {
  MEETING = 'MEETING',
  APPOINTMENT = 'APPOINTMENT',
  THERAPY = 'THERAPY',
  SUPPORT_GROUP = 'SUPPORT_GROUP',
  PERSONAL = 'PERSONAL',
  REMINDER = 'REMINDER',
}

export class CreateEventDto {
  @IsString()
  title: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsDateString()
  startTime: string;

  @IsOptional()
  @IsDateString()
  endTime?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsEnum(EventType)
  type?: EventType;

  @IsOptional()
  @IsBoolean()
  isRecurring?: boolean;

  @IsOptional()
  @IsString()
  recurrence?: string;
}
