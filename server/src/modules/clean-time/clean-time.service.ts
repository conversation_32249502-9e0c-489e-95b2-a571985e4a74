import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCleanTimeDto } from './dto/create-clean-time.dto';
import { UpdateCleanTimeDto } from './dto/update-clean-time.dto';

@Injectable()
export class CleanTimeService {
  constructor(private prisma: PrismaService) {}

  async create(createCleanTimeDto: CreateCleanTimeDto, userId: string) {
    // End any currently active clean time periods
    await this.prisma.cleanTime.updateMany({
      where: { userId, isActive: true },
      data: { isActive: false, endDate: new Date() },
    });

    return this.prisma.cleanTime.create({
      data: {
        ...createCleanTimeDto,
        startDate: new Date(createCleanTimeDto.startDate),
        userId,
      },
    });
  }

  async findAll(userId: string) {
    return this.prisma.cleanTime.findMany({
      where: { userId },
      orderBy: { startDate: 'desc' },
    });
  }

  async findCurrent(userId: string) {
    return this.prisma.cleanTime.findFirst({
      where: { userId, isActive: true },
      orderBy: { startDate: 'desc' },
    });
  }

  async findOne(id: string, userId: string) {
    const cleanTime = await this.prisma.cleanTime.findUnique({
      where: { id },
    });

    if (!cleanTime) {
      throw new NotFoundException('Clean time record not found');
    }

    if (cleanTime.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return cleanTime;
  }

  async update(id: string, updateCleanTimeDto: UpdateCleanTimeDto, userId: string) {
    const cleanTime = await this.prisma.cleanTime.findUnique({
      where: { id },
    });

    if (!cleanTime) {
      throw new NotFoundException('Clean time record not found');
    }

    if (cleanTime.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return this.prisma.cleanTime.update({
      where: { id },
      data: {
        ...updateCleanTimeDto,
        startDate: updateCleanTimeDto.startDate ? new Date(updateCleanTimeDto.startDate) : undefined,
        endDate: updateCleanTimeDto.endDate ? new Date(updateCleanTimeDto.endDate) : undefined,
      },
    });
  }

  async remove(id: string, userId: string) {
    const cleanTime = await this.prisma.cleanTime.findUnique({
      where: { id },
    });

    if (!cleanTime) {
      throw new NotFoundException('Clean time record not found');
    }

    if (cleanTime.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return this.prisma.cleanTime.delete({
      where: { id },
    });
  }

  async getStats(userId: string) {
    const current = await this.findCurrent(userId);
    const all = await this.findAll(userId);

    if (!current) {
      return {
        currentStreak: null,
        totalPeriods: all.length,
        longestStreak: this.calculateLongestStreak(all),
      };
    }

    const currentDays = Math.floor(
      (new Date().getTime() - current.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    return {
      currentStreak: {
        days: currentDays,
        startDate: current.startDate,
        notes: current.notes,
      },
      totalPeriods: all.length,
      longestStreak: this.calculateLongestStreak(all),
    };
  }

  private calculateLongestStreak(cleanTimes: any[]) {
    if (cleanTimes.length === 0) return null;

    let longest = 0;
    let longestRecord = null;

    for (const record of cleanTimes) {
      const endDate = record.endDate || new Date();
      const days = Math.floor(
        (endDate.getTime() - record.startDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (days > longest) {
        longest = days;
        longestRecord = record;
      }
    }

    return {
      days: longest,
      startDate: longestRecord.startDate,
      endDate: longestRecord.endDate,
      notes: longestRecord.notes,
    };
  }
}
