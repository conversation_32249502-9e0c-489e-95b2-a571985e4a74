import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { CleanTimeService } from './clean-time.service';
import { CreateCleanTimeDto } from './dto/create-clean-time.dto';
import { UpdateCleanTimeDto } from './dto/update-clean-time.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('clean-time')
@UseGuards(JwtAuthGuard)
export class CleanTimeController {
  constructor(private readonly cleanTimeService: CleanTimeService) {}

  @Post()
  create(@Body() createCleanTimeDto: CreateCleanTimeDto, @Request() req) {
    return this.cleanTimeService.create(createCleanTimeDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req) {
    return this.cleanTimeService.findAll(req.user.userId);
  }

  @Get('current')
  findCurrent(@Request() req) {
    return this.cleanTimeService.findCurrent(req.user.userId);
  }

  @Get('stats')
  getStats(@Request() req) {
    return this.cleanTimeService.getStats(req.user.userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.cleanTimeService.findOne(id, req.user.userId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCleanTimeDto: UpdateCleanTimeDto, @Request() req) {
    return this.cleanTimeService.update(id, updateCleanTimeDto, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.cleanTimeService.remove(id, req.user.userId);
  }
}
