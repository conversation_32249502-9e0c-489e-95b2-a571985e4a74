import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from './prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { JournalModule } from './journal/journal.module';
import { EventsModule } from './events/events.module';
import { PanicModule } from './panic/panic.module';
import { CleanTimeModule } from './clean-time/clean-time.module';
import { OrganizationsModule } from './organizations/organizations.module';
import { HousesModule } from './houses/houses.module';
import { MaintenanceModule } from './maintenance/maintenance.module';
import { QuotesModule } from './quotes/quotes.module';
import { GratitudeModule } from './gratitude/gratitude.module';
import { MeetingsModule } from './meetings/meetings.module';
import { SocialModule } from './social/social.module';
import { ChatModule } from './chat/chat.module';
import { ModerationModule } from './moderation/moderation.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    PrismaModule,
    AuthModule,
    UsersModule,
    JournalModule,
    EventsModule,
    PanicModule,
    CleanTimeModule,
    OrganizationsModule,
    HousesModule,
    MaintenanceModule,
    QuotesModule,
    GratitudeModule,
    MeetingsModule,
    SocialModule,
    ChatModule,
    ModerationModule,
  ],
})
export class AppModule {}