import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateMaintenanceDto } from './dto/create-maintenance.dto';
import { UpdateMaintenanceDto } from './dto/update-maintenance.dto';

@Injectable()
export class MaintenanceService {
  constructor(private prisma: PrismaService) {}

  async create(createMaintenanceDto: CreateMaintenanceDto, userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    // Verify house belongs to user's organization
    const house = await this.prisma.house.findFirst({
      where: {
        id: createMaintenanceDto.houseId,
        organizationId: user.organizationId,
      },
    });

    if (!house) {
      throw new NotFoundException('House not found or access denied');
    }

    return this.prisma.maintenanceRequest.create({
      data: {
        ...createMaintenanceDto,
        priority: createMaintenanceDto.priority || 'medium',
      },
      include: {
        house: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
      },
    });
  }

  async findAll(userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    return this.prisma.maintenanceRequest.findMany({
      where: {
        house: {
          organizationId: user.organizationId,
        },
      },
      include: {
        house: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
      },
      orderBy: [
        { status: 'asc' }, // pending first
        { priority: 'desc' }, // high priority first
        { createdAt: 'desc' },
      ],
    });
  }

  async findByHouse(houseId: string, userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    // Verify house belongs to user's organization
    const house = await this.prisma.house.findFirst({
      where: {
        id: houseId,
        organizationId: user.organizationId,
      },
    });

    if (!house) {
      throw new NotFoundException('House not found or access denied');
    }

    return this.prisma.maintenanceRequest.findMany({
      where: { houseId },
      include: {
        house: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
      },
      orderBy: [
        { status: 'asc' },
        { priority: 'desc' },
        { createdAt: 'desc' },
      ],
    });
  }

  async findOne(id: string, userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    const maintenanceRequest = await this.prisma.maintenanceRequest.findFirst({
      where: {
        id,
        house: {
          organizationId: user.organizationId,
        },
      },
      include: {
        house: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
      },
    });

    if (!maintenanceRequest) {
      throw new NotFoundException('Maintenance request not found');
    }

    return maintenanceRequest;
  }

  async update(id: string, updateMaintenanceDto: UpdateMaintenanceDto, userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    // Verify maintenance request belongs to user's organization
    const maintenanceRequest = await this.prisma.maintenanceRequest.findFirst({
      where: {
        id,
        house: {
          organizationId: user.organizationId,
        },
      },
    });

    if (!maintenanceRequest) {
      throw new NotFoundException('Maintenance request not found');
    }

    return this.prisma.maintenanceRequest.update({
      where: { id },
      data: updateMaintenanceDto,
      include: {
        house: {
          select: {
            id: true,
            name: true,
            address: true,
          },
        },
      },
    });
  }

  async remove(id: string, userId: string) {
    // Get user's organization and check permissions
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    if (user.role !== 'admin') {
      throw new ForbiddenException('Only organization admins can delete maintenance requests');
    }

    // Verify maintenance request belongs to user's organization
    const maintenanceRequest = await this.prisma.maintenanceRequest.findFirst({
      where: {
        id,
        house: {
          organizationId: user.organizationId,
        },
      },
    });

    if (!maintenanceRequest) {
      throw new NotFoundException('Maintenance request not found');
    }

    return this.prisma.maintenanceRequest.delete({
      where: { id },
    });
  }
}
