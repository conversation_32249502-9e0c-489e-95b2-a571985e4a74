import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { MaintenanceService } from './maintenance.service';
import { CreateMaintenanceDto } from './dto/create-maintenance.dto';
import { UpdateMaintenanceDto } from './dto/update-maintenance.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('maintenance')
@UseGuards(JwtAuthGuard)
export class MaintenanceController {
  constructor(private readonly maintenanceService: MaintenanceService) {}

  @Post()
  create(@Body() createMaintenanceDto: CreateMaintenanceDto, @Request() req) {
    return this.maintenanceService.create(createMaintenanceDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req, @Query('houseId') houseId?: string) {
    if (houseId) {
      return this.maintenanceService.findByHouse(houseId, req.user.userId);
    }
    return this.maintenanceService.findAll(req.user.userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.maintenanceService.findOne(id, req.user.userId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateMaintenanceDto: UpdateMaintenanceDto, @Request() req) {
    return this.maintenanceService.update(id, updateMaintenanceDto, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.maintenanceService.remove(id, req.user.userId);
  }
}
