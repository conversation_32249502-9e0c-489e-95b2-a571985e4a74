import { IsString, IsOptional, IsEnum, MaxLength } from 'class-validator';
import { MaintenancePriority, MaintenanceStatus } from './create-maintenance.dto';

export class UpdateMaintenanceDto {
  @IsOptional()
  @IsString()
  @MaxLength(200)
  title?: string;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  description?: string;

  @IsOptional()
  @IsEnum(MaintenancePriority)
  priority?: MaintenancePriority;

  @IsOptional()
  @IsEnum(MaintenanceStatus)
  status?: MaintenanceStatus;

  @IsOptional()
  @IsString()
  @MaxLength(1000)
  notes?: string;
}
