import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { HousesService } from './houses.service';
import { CreateHouseDto } from './dto/create-house.dto';
import { UpdateHouseDto } from './dto/update-house.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('houses')
@UseGuards(JwtAuthGuard)
export class HousesController {
  constructor(private readonly housesService: HousesService) {}

  @Post()
  create(@Body() createHouseDto: CreateHouseDto, @Request() req) {
    return this.housesService.create(createHouseDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req) {
    return this.housesService.findAll(req.user.userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.housesService.findOne(id, req.user.userId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateHouseDto: UpdateHouseDto, @Request() req) {
    return this.housesService.update(id, updateHouseDto, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.housesService.remove(id, req.user.userId);
  }
}
