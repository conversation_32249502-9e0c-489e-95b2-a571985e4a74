import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateHouseDto } from './dto/create-house.dto';
import { UpdateHouseDto } from './dto/update-house.dto';

@Injectable()
export class HousesService {
  constructor(private prisma: PrismaService) {}

  async create(createHouseDto: CreateHouseDto, userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization to create houses');
    }

    if (user.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can create houses');
    }

    return this.prisma.house.create({
      data: {
        ...createHouseDto,
        organizationId: user.organizationId,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        maintenanceRequests: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
      },
    });
  }

  async findAll(userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization to view houses');
    }

    return this.prisma.house.findMany({
      where: {
        organizationId: user.organizationId,
        isActive: true,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        maintenanceRequests: {
          where: { status: { not: 'completed' } },
          orderBy: { createdAt: 'desc' },
          take: 3,
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOne(id: string, userId: string) {
    // Get user's organization
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization to view houses');
    }

    const house = await this.prisma.house.findFirst({
      where: {
        id,
        organizationId: user.organizationId,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        maintenanceRequests: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!house) {
      throw new NotFoundException('House not found');
    }

    return house;
  }

  async update(id: string, updateHouseDto: UpdateHouseDto, userId: string) {
    // Get user's organization and check permissions
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    if (user.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can update houses');
    }

    // Verify house belongs to user's organization
    const house = await this.prisma.house.findFirst({
      where: {
        id,
        organizationId: user.organizationId,
      },
    });

    if (!house) {
      throw new NotFoundException('House not found');
    }

    return this.prisma.house.update({
      where: { id },
      data: updateHouseDto,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        maintenanceRequests: {
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
      },
    });
  }

  async remove(id: string, userId: string) {
    // Get user's organization and check permissions
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { organizationId: true, role: true },
    });

    if (!user?.organizationId) {
      throw new ForbiddenException('User must belong to an organization');
    }

    if (user.role !== 'ADMIN') {
      throw new ForbiddenException('Only organization admins can delete houses');
    }

    // Verify house belongs to user's organization
    const house = await this.prisma.house.findFirst({
      where: {
        id,
        organizationId: user.organizationId,
      },
    });

    if (!house) {
      throw new NotFoundException('House not found');
    }

    // Soft delete by setting isActive to false
    return this.prisma.house.update({
      where: { id },
      data: { isActive: false },
    });
  }
}
