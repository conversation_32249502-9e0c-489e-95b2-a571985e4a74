import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { ModerationService } from './moderation.service';
import { CreateReportDto, ModerateReportDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('moderation')
@UseGuards(JwtAuthGuard)
export class ModerationController {
  constructor(private readonly moderationService: ModerationService) {}

  @Post('reports')
  createReport(@Body() createReportDto: CreateReportDto, @Request() req) {
    return this.moderationService.createReport(createReportDto, req.user.userId);
  }

  @Get('reports')
  getReports(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') status?: string,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 20;
    return this.moderationService.getReports(pageNum, limitNum, status);
  }

  @Get('reports/stats')
  getReportStats() {
    return this.moderationService.getReportStats();
  }

  @Get('reports/:id')
  getReport(@Param('id') id: string) {
    return this.moderationService.getReport(id);
  }

  @Patch('reports/:id')
  moderateReport(
    @Param('id') id: string,
    @Body() moderateReportDto: ModerateReportDto,
    @Request() req,
  ) {
    return this.moderationService.moderateReport(id, moderateReportDto, req.user.userId);
  }

  @Delete('reports/:id')
  deleteReport(@Param('id') id: string, @Request() req) {
    return this.moderationService.deleteReport(id, req.user.userId);
  }
}
