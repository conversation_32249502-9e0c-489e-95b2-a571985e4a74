import { IsString, IsOptional, IsIn } from 'class-validator';

export class CreateReportDto {
  @IsString()
  @IsIn(['POST', 'MESSAGE', 'USER'])
  contentType: string;

  @IsString()
  contentId: string;

  @IsString()
  @IsIn(['SPAM', 'HARASSMENT', 'INAPPROPRIATE_CONTENT', 'HATE_SPEECH', 'VIOLENCE', 'OTHER'])
  reason: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  reportedUserId?: string;
}
