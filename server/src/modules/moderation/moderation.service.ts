import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { CreateReportDto, ModerateReportDto } from './dto';

@Injectable()
export class ModerationService {
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService,
  ) {}

  async createReport(createReportDto: CreateReportDto, reporterId: string) {
    // Validate that the content exists
    await this.validateContentExists(createReportDto.contentType, createReportDto.contentId);

    // Check if user already reported this content
    const existingReport = await this.prisma.report.findFirst({
      where: {
        reporterId,
        contentType: createReportDto.contentType,
        contentId: createReportDto.contentId,
      },
    });

    if (existingReport) {
      throw new BadRequestException('You have already reported this content');
    }

    // Create the report
    const report = await this.prisma.report.create({
      data: {
        reporterId,
        contentType: createReportDto.contentType,
        contentId: createReportDto.contentId,
        reason: createReportDto.reason,
        description: createReportDto.description,
        reportedUserId: createReportDto.reportedUserId,
        // Set specific content relations
        postId: createReportDto.contentType === 'POST' ? createReportDto.contentId : null,
        messageId: createReportDto.contentType === 'MESSAGE' ? createReportDto.contentId : null,
      },
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        reportedUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
    });

    // TODO: Integrate with OpenAI Moderation API for auto-flagging
    // await this.checkContentWithAI(createReportDto.contentType, createReportDto.contentId);

    return report;
  }

  async getReports(page: number = 1, limit: number = 20, status?: string) {
    const skip = (page - 1) * limit;
    const where: any = {};

    if (status) {
      where.status = status;
    }

    const [reports, total] = await Promise.all([
      this.prisma.report.findMany({
        where,
        include: {
          reporter: {
            select: {
              id: true,
              username: true,
              firstName: true,
            },
          },
          reportedUser: {
            select: {
              id: true,
              username: true,
              firstName: true,
            },
          },
          moderator: {
            select: {
              id: true,
              username: true,
              firstName: true,
            },
          },
          post: {
            select: {
              id: true,
              content: true,
              createdAt: true,
            },
          },
          message: {
            select: {
              id: true,
              content: true,
              createdAt: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      this.prisma.report.count({ where }),
    ]);

    return {
      reports,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getReport(id: string) {
    const report = await this.prisma.report.findUnique({
      where: { id },
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        reportedUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        moderator: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        post: {
          select: {
            id: true,
            content: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
              },
            },
          },
        },
        message: {
          select: {
            id: true,
            content: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
              },
            },
          },
        },
      },
    });

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    return report;
  }

  async moderateReport(id: string, moderateReportDto: ModerateReportDto, moderatorId: string) {
    const report = await this.prisma.report.findUnique({
      where: { id },
    });

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    // Check if user has moderator permissions
    const moderator = await this.prisma.user.findUnique({
      where: { id: moderatorId },
    });

    if (!moderator || !['ADMIN', 'STAFF'].includes(moderator.role)) {
      throw new ForbiddenException('Insufficient permissions to moderate reports');
    }

    const updatedReport = await this.prisma.report.update({
      where: { id },
      data: {
        status: moderateReportDto.status,
        moderatorNotes: moderateReportDto.moderatorNotes,
        moderatorId,
        resolvedAt: ['RESOLVED', 'DISMISSED'].includes(moderateReportDto.status) 
          ? new Date() 
          : null,
      },
      include: {
        reporter: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        reportedUser: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        moderator: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
    });

    return updatedReport;
  }

  async deleteReport(id: string, userId: string) {
    const report = await this.prisma.report.findUnique({
      where: { id },
    });

    if (!report) {
      throw new NotFoundException('Report not found');
    }

    // Only the reporter or a moderator can delete a report
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (report.reporterId !== userId && !['ADMIN', 'STAFF'].includes(user?.role || '')) {
      throw new ForbiddenException('You can only delete your own reports');
    }

    return this.prisma.report.delete({
      where: { id },
    });
  }

  private async validateContentExists(contentType: string, contentId: string) {
    switch (contentType) {
      case 'POST':
        const post = await this.prisma.post.findUnique({
          where: { id: contentId },
        });
        if (!post) {
          throw new NotFoundException('Post not found');
        }
        break;
      case 'MESSAGE':
        const message = await this.prisma.message.findUnique({
          where: { id: contentId },
        });
        if (!message) {
          throw new NotFoundException('Message not found');
        }
        break;
      case 'USER':
        const user = await this.prisma.user.findUnique({
          where: { id: contentId },
        });
        if (!user) {
          throw new NotFoundException('User not found');
        }
        break;
      default:
        throw new BadRequestException('Invalid content type');
    }
  }

  // TODO: Implement OpenAI Moderation API integration
  private async checkContentWithAI(contentType: string, contentId: string) {
    // This would integrate with OpenAI's Moderation API
    // to automatically flag potentially harmful content
    console.log(`TODO: Check ${contentType} ${contentId} with AI moderation`);
  }

  async getReportStats() {
    const [total, pending, reviewed, resolved, dismissed] = await Promise.all([
      this.prisma.report.count(),
      this.prisma.report.count({ where: { status: 'PENDING' } }),
      this.prisma.report.count({ where: { status: 'REVIEWED' } }),
      this.prisma.report.count({ where: { status: 'RESOLVED' } }),
      this.prisma.report.count({ where: { status: 'DISMISSED' } }),
    ]);

    return {
      total,
      pending,
      reviewed,
      resolved,
      dismissed,
    };
  }
}
