import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { EncryptionService } from './encryption.service';
import { CreateJournalDto } from './dto/create-journal.dto';
import { UpdateJournalDto } from './dto/update-journal.dto';

@Injectable()
export class JournalService {
  constructor(
    private prisma: PrismaService,
    private encryptionService: EncryptionService,
  ) {}

  private formatTags(tags: string | null): string[] {
    return tags ? tags.split(',').filter(tag => tag.trim()) : [];
  }

  async create(createJournalDto: CreateJournalDto, userId: string) {
    // Encrypt the content before storing
    const encryptedContent = await this.encryptionService.encrypt(createJournalDto.content);

    // If meetingId is provided, verify the meeting exists
    if (createJournalDto.meetingId) {
      const meeting = await this.prisma.meeting.findUnique({
        where: { id: createJournalDto.meetingId },
      });
      if (!meeting) {
        throw new NotFoundException('Meeting not found');
      }
    }

    const journal = await this.prisma.journal.create({
      data: {
        title: createJournalDto.title,
        encryptedContent,
        mood: createJournalDto.mood,
        tags: createJournalDto.tags ? createJournalDto.tags.join(',') : null,
        meetingId: createJournalDto.meetingId,
        userId,
      },
      select: {
        id: true,
        title: true,
        mood: true,
        tags: true,
        meetingId: true,
        createdAt: true,
        updatedAt: true,
      },
      include: {
        meeting: {
          select: {
            id: true,
            title: true,
            startTime: true,
          },
        },
      },
    });

    return {
      ...journal,
      tags: this.formatTags(journal.tags),
    };
  }

  async findAll(userId: string, meetingId?: string) {
    const where: any = { userId };
    if (meetingId) {
      where.meetingId = meetingId;
    }

    const journals = await this.prisma.journal.findMany({
      where,
      select: {
        id: true,
        title: true,
        mood: true,
        tags: true,
        meetingId: true,
        createdAt: true,
        updatedAt: true,
      },
      include: {
        meeting: {
          select: {
            id: true,
            title: true,
            startTime: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    return journals.map(journal => ({
      ...journal,
      tags: this.formatTags(journal.tags),
    }));
  }

  async findOne(id: string, userId: string) {
    const journal = await this.prisma.journal.findUnique({
      where: { id },
    });

    if (!journal) {
      throw new NotFoundException('Journal entry not found');
    }

    if (journal.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Decrypt the content before returning
    const decryptedContent = await this.encryptionService.decrypt(journal.encryptedContent);

    return {
      id: journal.id,
      title: journal.title,
      content: decryptedContent,
      mood: journal.mood,
      tags: this.formatTags(journal.tags),
      createdAt: journal.createdAt,
      updatedAt: journal.updatedAt,
    };
  }

  async update(id: string, updateJournalDto: UpdateJournalDto, userId: string) {
    const journal = await this.prisma.journal.findUnique({
      where: { id },
    });

    if (!journal) {
      throw new NotFoundException('Journal entry not found');
    }

    if (journal.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    const updateData: any = {
      title: updateJournalDto.title,
      mood: updateJournalDto.mood,
      tags: updateJournalDto.tags ? updateJournalDto.tags.join(',') : undefined,
    };

    // Encrypt content if provided
    if (updateJournalDto.content) {
      updateData.encryptedContent = await this.encryptionService.encrypt(updateJournalDto.content);
    }

    const updatedJournal = await this.prisma.journal.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        title: true,
        mood: true,
        tags: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      ...updatedJournal,
      tags: this.formatTags(updatedJournal.tags),
    };
  }

  async remove(id: string, userId: string) {
    const journal = await this.prisma.journal.findUnique({
      where: { id },
    });

    if (!journal) {
      throw new NotFoundException('Journal entry not found');
    }

    if (journal.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return this.prisma.journal.delete({
      where: { id },
    });
  }
}
