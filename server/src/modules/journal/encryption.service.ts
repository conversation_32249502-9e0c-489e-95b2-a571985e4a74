import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sodium from 'libsodium-wrappers';

@Injectable()
export class EncryptionService {
  private encryptionKey: Uint8Array;

  constructor(private configService: ConfigService) {
    this.initializeEncryption();
  }

  private async initializeEncryption() {
    await sodium.ready;
    
    // In production, use a proper key derivation function
    const keyString = this.configService.get('ENCRYPTION_KEY') || 'dev-encryption-key-32-chars-long';
    
    // Ensure key is exactly 32 bytes for AES-256
    const keyBuffer = Buffer.from(keyString.padEnd(32, '0').slice(0, 32), 'utf8');
    this.encryptionKey = new Uint8Array(keyBuffer);
  }

  async encrypt(plaintext: string): Promise<string> {
    await sodium.ready;
    
    // Generate a random nonce
    const nonce = sodium.randombytes_buf(sodium.crypto_secretbox_NONCEBYTES);
    
    // Encrypt the plaintext
    const ciphertext = sodium.crypto_secretbox_easy(plaintext, nonce, this.encryptionKey);
    
    // Combine nonce and ciphertext
    const combined = new Uint8Array(nonce.length + ciphertext.length);
    combined.set(nonce);
    combined.set(ciphertext, nonce.length);
    
    // Return as base64 string
    return sodium.to_base64(combined);
  }

  async decrypt(encryptedData: string): Promise<string> {
    await sodium.ready;
    
    try {
      // Decode from base64
      const combined = sodium.from_base64(encryptedData);
      
      // Extract nonce and ciphertext
      const nonce = combined.slice(0, sodium.crypto_secretbox_NONCEBYTES);
      const ciphertext = combined.slice(sodium.crypto_secretbox_NONCEBYTES);
      
      // Decrypt
      const plaintext = sodium.crypto_secretbox_open_easy(ciphertext, nonce, this.encryptionKey);
      
      return sodium.to_string(plaintext);
    } catch (error) {
      throw new Error('Failed to decrypt data');
    }
  }
}
