import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { JournalService } from './journal.service';
import { CreateJournalDto } from './dto/create-journal.dto';
import { UpdateJournalDto } from './dto/update-journal.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('journal')
@UseGuards(JwtAuthGuard)
export class JournalController {
  constructor(private readonly journalService: JournalService) {}

  @Post()
  create(@Body() createJournalDto: CreateJournalDto, @Request() req) {
    return this.journalService.create(createJournalDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req, @Query('meetingId') meetingId?: string) {
    return this.journalService.findAll(req.user.userId, meetingId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.journalService.findOne(id, req.user.userId);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateJournalDto: UpdateJournalDto, @Request() req) {
    return this.journalService.update(id, updateJournalDto, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.journalService.remove(id, req.user.userId);
  }
}
