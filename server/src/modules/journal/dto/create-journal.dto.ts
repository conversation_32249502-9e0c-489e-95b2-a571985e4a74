import { IsString, IsOptional, <PERSON>I<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateJournalDto {
  @IsString()
  title: string;

  @IsString()
  content: string;

  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(10)
  mood?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @IsOptional()
  @IsString()
  meetingId?: string;
}
