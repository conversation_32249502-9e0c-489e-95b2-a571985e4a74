import { Injectable, NotFoundException, ForbiddenException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateMeetingDto, UpdateMeetingDto, RateMeetingDto } from './dto';

@Injectable()
export class MeetingsService {
  constructor(private prisma: PrismaService) {}

  async create(createMeetingDto: CreateMeetingDto) {
    return this.prisma.meeting.create({
      data: {
        ...createMeetingDto,
        startTime: new Date(createMeetingDto.startTime),
        endTime: createMeetingDto.endTime ? new Date(createMeetingDto.endTime) : null,
      },
      include: {
        ratings: {
          select: {
            rating: true,
          },
        },
        _count: {
          select: {
            ratings: true,
          },
        },
      },
    });
  }

  async findAll(page: number = 1, limit: number = 20) {
    const skip = (page - 1) * limit;
    
    const [meetings, total] = await Promise.all([
      this.prisma.meeting.findMany({
        where: { isActive: true },
        include: {
          ratings: {
            select: {
              rating: true,
            },
          },
          _count: {
            select: {
              ratings: true,
            },
          },
        },
        orderBy: { startTime: 'asc' },
        skip,
        take: limit,
      }),
      this.prisma.meeting.count({
        where: { isActive: true },
      }),
    ]);

    // Calculate average ratings
    const meetingsWithRatings = meetings.map(meeting => ({
      ...meeting,
      avgRating: meeting.ratings.length > 0 
        ? meeting.ratings.reduce((sum, r) => sum + r.rating, 0) / meeting.ratings.length 
        : 0,
      ratings: undefined, // Remove individual ratings from response
    }));

    return {
      meetings: meetingsWithRatings,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findNearby(lat: number, lng: number, radiusKm: number = 10, limit: number = 50) {
    // For SQLite, we'll use a simple distance calculation
    // In production with PostgreSQL, this would use PostGIS
    const meetings = await this.prisma.meeting.findMany({
      where: {
        isActive: true,
        latitude: { not: null },
        longitude: { not: null },
      },
      include: {
        ratings: {
          select: {
            rating: true,
          },
        },
        _count: {
          select: {
            ratings: true,
          },
        },
      },
      take: limit * 2, // Get more to filter by distance
    });

    // Calculate distance and filter
    const nearbyMeetings = meetings
      .map(meeting => {
        const distance = this.calculateDistance(lat, lng, meeting.latitude!, meeting.longitude!);
        return {
          ...meeting,
          distance,
          avgRating: meeting.ratings.length > 0 
            ? meeting.ratings.reduce((sum, r) => sum + r.rating, 0) / meeting.ratings.length 
            : 0,
          ratings: undefined,
        };
      })
      .filter(meeting => meeting.distance <= radiusKm)
      .sort((a, b) => a.distance - b.distance)
      .slice(0, limit);

    return nearbyMeetings;
  }

  async findOne(id: string) {
    const meeting = await this.prisma.meeting.findUnique({
      where: { id },
      include: {
        ratings: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        _count: {
          select: {
            ratings: true,
          },
        },
      },
    });

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    const avgRating = meeting.ratings.length > 0 
      ? meeting.ratings.reduce((sum, r) => sum + r.rating, 0) / meeting.ratings.length 
      : 0;

    return {
      ...meeting,
      avgRating,
    };
  }

  async update(id: string, updateMeetingDto: UpdateMeetingDto) {
    const meeting = await this.prisma.meeting.findUnique({
      where: { id },
    });

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    return this.prisma.meeting.update({
      where: { id },
      data: {
        ...updateMeetingDto,
        startTime: updateMeetingDto.startTime ? new Date(updateMeetingDto.startTime) : undefined,
        endTime: updateMeetingDto.endTime ? new Date(updateMeetingDto.endTime) : undefined,
      },
    });
  }

  async remove(id: string) {
    const meeting = await this.prisma.meeting.findUnique({
      where: { id },
    });

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    return this.prisma.meeting.update({
      where: { id },
      data: { isActive: false },
    });
  }

  async rateMeeting(meetingId: string, userId: string, rateMeetingDto: RateMeetingDto) {
    // Check if meeting exists
    const meeting = await this.prisma.meeting.findUnique({
      where: { id: meetingId },
    });

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    return this.prisma.meetingRating.upsert({
      where: {
        meetingId_userId: { meetingId, userId },
      },
      update: {
        rating: rateMeetingDto.rating,
        review: rateMeetingDto.review,
      },
      create: {
        meetingId,
        userId,
        rating: rateMeetingDto.rating,
        review: rateMeetingDto.review,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
    });
  }

  async getMeetingRatings(meetingId: string) {
    const meeting = await this.prisma.meeting.findUnique({
      where: { id: meetingId },
    });

    if (!meeting) {
      throw new NotFoundException('Meeting not found');
    }

    return this.prisma.meetingRating.findMany({
      where: { meetingId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  // Helper method to calculate distance between two points (Haversine formula)
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const d = R * c; // Distance in kilometers
    return d;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI / 180);
  }
}
