import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateQuoteDto } from './dto/create-quote.dto';
import { UpdateQuoteDto } from './dto/update-quote.dto';

@Injectable()
export class QuotesService {
  constructor(private prisma: PrismaService) {}

  async create(createQuoteDto: CreateQuoteDto, userId: string) {
    return this.prisma.quote.create({
      data: {
        ...createQuoteDto,
        userId,
      },
      select: {
        id: true,
        text: true,
        tags: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  async findAll(userId: string, search?: string, tags?: string) {
    const where: any = { userId };

    // Add search filter if provided
    if (search) {
      where.text = {
        contains: search,
      };
    }

    // Add tags filter if provided
    if (tags) {
      where.tags = {
        contains: tags,
      };
    }

    return this.prisma.quote.findMany({
      where,
      select: {
        id: true,
        text: true,
        tags: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string, userId: string) {
    const quote = await this.prisma.quote.findUnique({
      where: { id },
      select: {
        id: true,
        text: true,
        tags: true,
        createdAt: true,
        updatedAt: true,
        userId: true,
      },
    });

    if (!quote) {
      throw new NotFoundException('Quote not found');
    }

    if (quote.userId !== userId) {
      throw new ForbiddenException('You can only access your own quotes');
    }

    return quote;
  }

  async update(id: string, updateQuoteDto: UpdateQuoteDto, userId: string) {
    // First check if quote exists and belongs to user
    await this.findOne(id, userId);

    return this.prisma.quote.update({
      where: { id },
      data: updateQuoteDto,
      select: {
        id: true,
        text: true,
        tags: true,
        createdAt: true,
        updatedAt: true,
      },
    });
  }

  async remove(id: string, userId: string) {
    // First check if quote exists and belongs to user
    await this.findOne(id, userId);

    await this.prisma.quote.delete({
      where: { id },
    });

    return { message: 'Quote deleted successfully' };
  }

  async getRandomQuote(userId: string) {
    const quotes = await this.prisma.quote.findMany({
      where: { userId },
      select: {
        id: true,
        text: true,
        tags: true,
        createdAt: true,
      },
    });

    if (quotes.length === 0) {
      return null;
    }

    const randomIndex = Math.floor(Math.random() * quotes.length);
    return quotes[randomIndex];
  }
}
