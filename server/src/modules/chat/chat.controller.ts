import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Patch,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { ChatService } from './chat.service';
import { CreateRoomDto, SendMessageDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('chat')
@UseGuards(JwtAuthGuard)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('rooms')
  createRoom(@Body() createRoomDto: CreateRoomDto, @Request() req) {
    return this.chatService.createRoom(createRoomDto, req.user.userId);
  }

  @Get('rooms')
  getRooms(@Request() req) {
    return this.chatService.getRooms(req.user.userId);
  }

  @Get('rooms/:id')
  getRoom(@Param('id') id: string, @Request() req) {
    return this.chatService.getRoom(id, req.user.userId);
  }

  @Post('rooms/:id/messages')
  sendMessage(
    @Param('id') id: string,
    @Body() sendMessageDto: SendMessageDto,
    @Request() req,
  ) {
    return this.chatService.sendMessage(id, req.user.userId, sendMessageDto);
  }

  @Get('rooms/:id/messages')
  getMessages(
    @Param('id') id: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Request() req?,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 50;
    return this.chatService.getMessages(id, req.user.userId, pageNum, limitNum);
  }

  @Delete('messages/:id')
  deleteMessage(@Param('id') id: string, @Request() req) {
    return this.chatService.deleteMessage(id, req.user.userId);
  }

  @Patch('messages/:id')
  editMessage(
    @Param('id') id: string,
    @Body('content') content: string,
    @Request() req,
  ) {
    return this.chatService.editMessage(id, req.user.userId, content);
  }
}
