import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateRoomDto, SendMessageDto } from './dto';

@Injectable()
export class ChatService {
  constructor(private prisma: PrismaService) {}

  async createRoom(createRoomDto: CreateRoomDto, userId: string) {
    // If it's a meeting room, verify the meeting exists
    if (createRoomDto.meetingId) {
      const meeting = await this.prisma.meeting.findUnique({
        where: { id: createRoomDto.meetingId },
      });

      if (!meeting) {
        throw new NotFoundException('Meeting not found');
      }
    }

    return this.prisma.chatRoom.create({
      data: {
        name: createRoomDto.name,
        roomType: createRoomDto.roomType,
        meetingId: createRoomDto.meetingId,
        isPrivate: createRoomDto.isPrivate || false,
        createdBy: userId,
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        meeting: {
          select: {
            id: true,
            title: true,
          },
        },
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });
  }

  async getRooms(userId: string) {
    // For now, return all public rooms and rooms created by the user
    // In a more complex system, you'd have room memberships
    return this.prisma.chatRoom.findMany({
      where: {
        OR: [
          { isPrivate: false },
          { createdBy: userId },
        ],
      },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        meeting: {
          select: {
            id: true,
            title: true,
          },
        },
        _count: {
          select: {
            messages: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getRoom(id: string, userId: string) {
    const room = await this.prisma.chatRoom.findUnique({
      where: { id },
      include: {
        creator: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
        meeting: {
          select: {
            id: true,
            title: true,
          },
        },
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    if (!room) {
      throw new NotFoundException('Chat room not found');
    }

    // Check access permissions
    if (room.isPrivate && room.createdBy !== userId) {
      throw new ForbiddenException('Access denied to private room');
    }

    return room;
  }

  async sendMessage(roomId: string, userId: string, sendMessageDto: SendMessageDto) {
    // Verify room exists and user has access
    await this.getRoom(roomId, userId);

    return this.prisma.message.create({
      data: {
        roomId,
        userId,
        content: sendMessageDto.content,
        messageType: sendMessageDto.messageType || 'TEXT',
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
    });
  }

  async getMessages(roomId: string, userId: string, page: number = 1, limit: number = 50) {
    // Verify room exists and user has access
    await this.getRoom(roomId, userId);

    const skip = (page - 1) * limit;

    return this.prisma.message.findMany({
      where: { roomId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    });
  }

  async deleteMessage(messageId: string, userId: string) {
    const message = await this.prisma.message.findUnique({
      where: { id: messageId },
    });

    if (!message) {
      throw new NotFoundException('Message not found');
    }

    if (message.userId !== userId) {
      throw new ForbiddenException('You can only delete your own messages');
    }

    return this.prisma.message.delete({
      where: { id: messageId },
    });
  }

  async editMessage(messageId: string, userId: string, content: string) {
    const message = await this.prisma.message.findUnique({
      where: { id: messageId },
    });

    if (!message) {
      throw new NotFoundException('Message not found');
    }

    if (message.userId !== userId) {
      throw new ForbiddenException('You can only edit your own messages');
    }

    return this.prisma.message.update({
      where: { id: messageId },
      data: {
        content,
        isEdited: true,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
    });
  }
}
