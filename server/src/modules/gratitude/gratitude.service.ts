import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateGratitudeDto } from './dto/create-gratitude.dto';

@Injectable()
export class GratitudeService {
  constructor(private prisma: PrismaService) {}

  async create(createGratitudeDto: CreateGratitudeDto, userId: string) {
    return this.prisma.gratitudeEntry.create({
      data: {
        ...createGratitudeDto,
        userId,
      },
      select: {
        id: true,
        text: true,
        createdAt: true,
      },
    });
  }

  async findAll(userId: string, limit?: number) {
    return this.prisma.gratitudeEntry.findMany({
      where: { userId },
      select: {
        id: true,
        text: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: limit,
    });
  }

  async findRecent(userId: string, days: number = 7) {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return this.prisma.gratitudeEntry.findMany({
      where: {
        userId,
        createdAt: {
          gte: startDate,
        },
      },
      select: {
        id: true,
        text: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  async findOne(id: string, userId: string) {
    const gratitudeEntry = await this.prisma.gratitudeEntry.findUnique({
      where: { id },
      select: {
        id: true,
        text: true,
        createdAt: true,
        userId: true,
      },
    });

    if (!gratitudeEntry) {
      throw new NotFoundException('Gratitude entry not found');
    }

    if (gratitudeEntry.userId !== userId) {
      throw new ForbiddenException('You can only access your own gratitude entries');
    }

    return gratitudeEntry;
  }

  async remove(id: string, userId: string) {
    // First check if gratitude entry exists and belongs to user
    await this.findOne(id, userId);

    await this.prisma.gratitudeEntry.delete({
      where: { id },
    });

    return { message: 'Gratitude entry deleted successfully' };
  }

  async getStats(userId: string) {
    const total = await this.prisma.gratitudeEntry.count({
      where: { userId },
    });

    const thisWeek = await this.prisma.gratitudeEntry.count({
      where: {
        userId,
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        },
      },
    });

    const thisMonth = await this.prisma.gratitudeEntry.count({
      where: {
        userId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        },
      },
    });

    return {
      total,
      thisWeek,
      thisMonth,
    };
  }
}
