import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GratitudeService } from './gratitude.service';
import { CreateGratitudeDto } from './dto/create-gratitude.dto';

@Controller('gratitude')
@UseGuards(JwtAuthGuard)
export class GratitudeController {
  constructor(private readonly gratitudeService: GratitudeService) {}

  @Post()
  create(@Body() createGratitudeDto: CreateGratitudeDto, @Request() req) {
    return this.gratitudeService.create(createGratitudeDto, req.user.userId);
  }

  @Get()
  findAll(@Request() req, @Query('limit') limit?: string) {
    const limitNum = limit ? parseInt(limit, 10) : undefined;
    return this.gratitudeService.findAll(req.user.userId, limitNum);
  }

  @Get('recent')
  findRecent(@Request() req, @Query('days') days?: string) {
    const daysNum = days ? parseInt(days, 10) : 7;
    return this.gratitudeService.findRecent(req.user.userId, daysNum);
  }

  @Get('stats')
  getStats(@Request() req) {
    return this.gratitudeService.getStats(req.user.userId);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.gratitudeService.findOne(id, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.gratitudeService.remove(id, req.user.userId);
  }
}
