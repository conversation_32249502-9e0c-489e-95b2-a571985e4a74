import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { PanicService } from './panic.service';
import { CreatePanicContactDto } from './dto/create-panic-contact.dto';
import { UpdatePanicContactDto } from './dto/update-panic-contact.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('panic')
@UseGuards(JwtAuthGuard)
export class PanicController {
  constructor(private readonly panicService: PanicService) {}

  @Post('contacts')
  create(@Body() createPanicContactDto: CreatePanicContactDto, @Request() req) {
    return this.panicService.create(createPanicContactDto, req.user.userId);
  }

  @Get('contacts')
  findAll(@Request() req) {
    return this.panicService.findAll(req.user.userId);
  }

  @Get('contacts/primary')
  findPrimary(@Request() req) {
    return this.panicService.findPrimary(req.user.userId);
  }

  @Get('contacts/:id')
  findOne(@Param('id') id: string, @Request() req) {
    return this.panicService.findOne(id, req.user.userId);
  }

  @Patch('contacts/:id')
  update(@Param('id') id: string, @Body() updatePanicContactDto: UpdatePanicContactDto, @Request() req) {
    return this.panicService.update(id, updatePanicContactDto, req.user.userId);
  }

  @Delete('contacts/:id')
  remove(@Param('id') id: string, @Request() req) {
    return this.panicService.remove(id, req.user.userId);
  }
}
