import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePanicContactDto } from './dto/create-panic-contact.dto';
import { UpdatePanicContactDto } from './dto/update-panic-contact.dto';

@Injectable()
export class PanicService {
  constructor(private prisma: PrismaService) {}

  async create(createPanicContactDto: CreatePanicContactDto, userId: string) {
    // If this is set as primary, unset other primary contacts
    if (createPanicContactDto.isPrimary) {
      await this.prisma.panicContact.updateMany({
        where: { userId, isPrimary: true },
        data: { isPrimary: false },
      });
    }

    return this.prisma.panicContact.create({
      data: {
        ...createPanicContactDto,
        userId,
      },
    });
  }

  async findAll(userId: string) {
    return this.prisma.panicContact.findMany({
      where: { userId, isActive: true },
      orderBy: [
        { isPrimary: 'desc' },
        { name: 'asc' },
      ],
    });
  }

  async findPrimary(userId: string) {
    return this.prisma.panicContact.findFirst({
      where: { userId, isPrimary: true, isActive: true },
    });
  }

  async findOne(id: string, userId: string) {
    const contact = await this.prisma.panicContact.findUnique({
      where: { id },
    });

    if (!contact) {
      throw new NotFoundException('Panic contact not found');
    }

    if (contact.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    return contact;
  }

  async update(id: string, updatePanicContactDto: UpdatePanicContactDto, userId: string) {
    const contact = await this.prisma.panicContact.findUnique({
      where: { id },
    });

    if (!contact) {
      throw new NotFoundException('Panic contact not found');
    }

    if (contact.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // If this is being set as primary, unset other primary contacts
    if (updatePanicContactDto.isPrimary) {
      await this.prisma.panicContact.updateMany({
        where: { userId, isPrimary: true, id: { not: id } },
        data: { isPrimary: false },
      });
    }

    return this.prisma.panicContact.update({
      where: { id },
      data: updatePanicContactDto,
    });
  }

  async remove(id: string, userId: string) {
    const contact = await this.prisma.panicContact.findUnique({
      where: { id },
    });

    if (!contact) {
      throw new NotFoundException('Panic contact not found');
    }

    if (contact.userId !== userId) {
      throw new ForbiddenException('Access denied');
    }

    // Soft delete by setting isActive to false
    return this.prisma.panicContact.update({
      where: { id },
      data: { isActive: false },
    });
  }
}
