import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePostDto, ReactToPostDto } from './dto';

@Injectable()
export class SocialService {
  constructor(private prisma: PrismaService) {}

  async createPost(createPostDto: CreatePostDto, userId: string) {
    // Convert mediaUrls array to JSON string for SQLite storage
    const mediaUrlsJson = createPostDto.mediaUrls ? JSON.stringify(createPostDto.mediaUrls) : null;

    return this.prisma.post.create({
      data: {
        content: createPostDto.content,
        mediaUrls: mediaUrlsJson,
        postType: createPostDto.postType || 'TEXT',
        isAnonymous: createPostDto.isAnonymous || false,
        userId,
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          },
        },
        reactions: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
              },
            },
          },
        },
        _count: {
          select: {
            reactions: true,
          },
        },
      },
    });
  }

  async getFeed(page: number = 1, limit: number = 20, userId?: string) {
    const skip = (page - 1) * limit;

    const posts = await this.prisma.post.findMany({
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          },
        },
        reactions: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
              },
            },
          },
        },
        _count: {
          select: {
            reactions: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    });

    // Parse mediaUrls JSON and add user reaction info
    const postsWithParsedMedia = posts.map(post => {
      const mediaUrls = post.mediaUrls ? JSON.parse(post.mediaUrls) : [];
      const userReaction = userId 
        ? post.reactions.find(r => r.userId === userId)
        : null;

      return {
        ...post,
        mediaUrls,
        userReaction: userReaction?.reactionType || null,
      };
    });

    return postsWithParsedMedia;
  }

  async getPost(id: string, userId?: string) {
    const post = await this.prisma.post.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
            lastName: true,
          },
        },
        reactions: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                firstName: true,
              },
            },
          },
        },
        _count: {
          select: {
            reactions: true,
          },
        },
      },
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const mediaUrls = post.mediaUrls ? JSON.parse(post.mediaUrls) : [];
    const userReaction = userId 
      ? post.reactions.find(r => r.userId === userId)
      : null;

    return {
      ...post,
      mediaUrls,
      userReaction: userReaction?.reactionType || null,
    };
  }

  async reactToPost(postId: string, userId: string, reactToPostDto: ReactToPostDto) {
    // Check if post exists
    const post = await this.prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    // Check if user already has this reaction type
    const existingReaction = await this.prisma.reaction.findUnique({
      where: {
        postId_userId_reactionType: {
          postId,
          userId,
          reactionType: reactToPostDto.reactionType,
        },
      },
    });

    if (existingReaction) {
      // Remove the reaction (toggle off)
      await this.prisma.reaction.delete({
        where: { id: existingReaction.id },
      });
      return { action: 'removed', reactionType: reactToPostDto.reactionType };
    } else {
      // Remove any other reaction types from this user for this post
      await this.prisma.reaction.deleteMany({
        where: {
          postId,
          userId,
        },
      });

      // Add the new reaction
      const reaction = await this.prisma.reaction.create({
        data: {
          postId,
          userId,
          reactionType: reactToPostDto.reactionType,
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              firstName: true,
            },
          },
        },
      });

      return { action: 'added', reaction };
    }
  }

  async deletePost(id: string, userId: string) {
    const post = await this.prisma.post.findUnique({
      where: { id },
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    if (post.userId !== userId) {
      throw new ForbiddenException('You can only delete your own posts');
    }

    return this.prisma.post.delete({
      where: { id },
    });
  }

  async getPostReactions(postId: string) {
    const post = await this.prisma.post.findUnique({
      where: { id: postId },
    });

    if (!post) {
      throw new NotFoundException('Post not found');
    }

    const reactions = await this.prisma.reaction.findMany({
      where: { postId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            firstName: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Group reactions by type
    const reactionGroups = reactions.reduce((groups, reaction) => {
      const type = reaction.reactionType;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(reaction);
      return groups;
    }, {} as Record<string, typeof reactions>);

    return {
      total: reactions.length,
      groups: reactionGroups,
    };
  }
}
