import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Request,
  Query,
} from '@nestjs/common';
import { SocialService } from './social.service';
import { CreatePostDto, ReactToPostDto } from './dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('social')
@UseGuards(JwtAuthGuard)
export class SocialController {
  constructor(private readonly socialService: SocialService) {}

  @Post('posts')
  createPost(@Body() createPostDto: CreatePostDto, @Request() req) {
    return this.socialService.createPost(createPostDto, req.user.userId);
  }

  @Get('feed')
  getFeed(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Request() req?,
  ) {
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 20;
    return this.socialService.getFeed(pageNum, limitNum, req?.user?.userId);
  }

  @Get('posts/:id')
  getPost(@Param('id') id: string, @Request() req) {
    return this.socialService.getPost(id, req.user.userId);
  }

  @Post('posts/:id/react')
  reactToPost(
    @Param('id') id: string,
    @Body() reactToPostDto: ReactToPostDto,
    @Request() req,
  ) {
    return this.socialService.reactToPost(id, req.user.userId, reactToPostDto);
  }

  @Get('posts/:id/reactions')
  getPostReactions(@Param('id') id: string) {
    return this.socialService.getPostReactions(id);
  }

  @Delete('posts/:id')
  deletePost(@Param('id') id: string, @Request() req) {
    return this.socialService.deletePost(id, req.user.userId);
  }
}
