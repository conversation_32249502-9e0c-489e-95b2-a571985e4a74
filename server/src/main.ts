import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './modules/app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Get config service
  const configService = app.get(ConfigService);

  // Enable CORS for frontend apps
  app.enableCors({
    origin: [
      'http://localhost:3000', // Next.js web app (default)
      'http://localhost:3002', // Next.js web app (alternate port)
      'http://localhost:8081', // Expo dev server
      'exp://localhost:8081',  // Expo app
    ],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // Global prefix for API routes
  app.setGlobalPrefix('api');

  const port = configService.get('PORT') || 3001;
  await app.listen(port);

  console.log(`🚀 Recovery Connect API running on http://localhost:${port}/api`);
}

bootstrap();