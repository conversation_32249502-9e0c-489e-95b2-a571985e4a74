const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testPermissionsAndSecurity() {
  console.log('🔐 Testing Permissions and Security\n');
  
  let adminToken = '';
  let userToken = '';
  let org1Id = '';
  let org2Id = '';
  let house1Id = '';
  let house2Id = '';

  try {
    // Create Admin User for Organization 1
    console.log('1️⃣ Creating Admin User for Org 1...');
    const admin1Response = await axios.post(`${API_BASE}/auth/register`, {
      email: `admin1${Date.now()}@org1.com`,
      username: `admin1${Date.now().toString().slice(-6)}`,
      password: 'password123',
      firstName: 'Admin',
      lastName: 'One'
    });
    
    const admin1Login = await axios.post(`${API_BASE}/auth/login`, {
      email: admin1Response.data.user.email,
      password: 'password123'
    });
    adminToken = admin1Login.data.access_token;
    console.log('✅ Admin 1 created and logged in');

    // Create Organization 1
    console.log('\n2️⃣ Creating Organization 1...');
    const org1Response = await axios.post(`${API_BASE}/organizations`, {
      name: 'Recovery Center Alpha',
      description: 'First test organization'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    org1Id = org1Response.data.id;
    console.log('✅ Organization 1 created:', org1Response.data.name);

    // Create House in Organization 1
    console.log('\n3️⃣ Creating House in Org 1...');
    const house1Response = await axios.post(`${API_BASE}/houses`, {
      name: 'Alpha House 1',
      address: '123 Alpha St',
      capacity: 10
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    house1Id = house1Response.data.id;
    console.log('✅ House created in Org 1:', house1Response.data.name);

    // Create Regular User for Organization 2
    console.log('\n4️⃣ Creating Regular User for Org 2...');
    const user2Response = await axios.post(`${API_BASE}/auth/register`, {
      email: `user2${Date.now()}@org2.com`,
      username: `user2${Date.now().toString().slice(-6)}`,
      password: 'password123',
      firstName: 'User',
      lastName: 'Two'
    });
    
    const user2Login = await axios.post(`${API_BASE}/auth/login`, {
      email: user2Response.data.user.email,
      password: 'password123'
    });
    userToken = user2Login.data.access_token;
    console.log('✅ User 2 created and logged in');

    // Create Organization 2
    console.log('\n5️⃣ Creating Organization 2...');
    const org2Response = await axios.post(`${API_BASE}/organizations`, {
      name: 'Recovery Center Beta',
      description: 'Second test organization'
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    org2Id = org2Response.data.id;
    console.log('✅ Organization 2 created:', org2Response.data.name);

    // Create House in Organization 2
    console.log('\n6️⃣ Creating House in Org 2...');
    const house2Response = await axios.post(`${API_BASE}/houses`, {
      name: 'Beta House 1',
      address: '456 Beta Ave',
      capacity: 8
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    house2Id = house2Response.data.id;
    console.log('✅ House created in Org 2:', house2Response.data.name);

    // Test Data Isolation - Admin 1 should NOT see Org 2 houses
    console.log('\n7️⃣ Testing Data Isolation...');
    const admin1HousesResponse = await axios.get(`${API_BASE}/houses`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    const admin1Houses = admin1HousesResponse.data;
    const hasOrg2House = admin1Houses.some(house => house.id === house2Id);
    
    if (hasOrg2House) {
      console.log('❌ Data isolation FAILED - Admin 1 can see Org 2 houses');
    } else {
      console.log('✅ Data isolation WORKING - Admin 1 can only see Org 1 houses');
      console.log(`   Admin 1 sees ${admin1Houses.length} house(s)`);
    }

    // Test Cross-Organization Access - User 2 should NOT access Org 1 house
    console.log('\n8️⃣ Testing Cross-Organization Access...');
    try {
      await axios.get(`${API_BASE}/houses/${house1Id}`, {
        headers: { Authorization: `Bearer ${userToken}` }
      });
      console.log('❌ Cross-org access FAILED - User 2 can access Org 1 house');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✅ Cross-org access BLOCKED - User 2 cannot access Org 1 house');
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status);
      }
    }

    // Test Maintenance Request Isolation
    console.log('\n9️⃣ Testing Maintenance Request Isolation...');
    
    // Create maintenance request in Org 1
    const maintenance1Response = await axios.post(`${API_BASE}/maintenance`, {
      title: 'Org 1 Maintenance',
      description: 'Test maintenance for org 1',
      priority: 'high',
      houseId: house1Id
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    
    // Create maintenance request in Org 2
    const maintenance2Response = await axios.post(`${API_BASE}/maintenance`, {
      title: 'Org 2 Maintenance',
      description: 'Test maintenance for org 2',
      priority: 'medium',
      houseId: house2Id
    }, {
      headers: { Authorization: `Bearer ${userToken}` }
    });

    // Check if User 2 can see Org 1 maintenance requests
    const user2MaintenanceResponse = await axios.get(`${API_BASE}/maintenance`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    
    const user2Maintenance = user2MaintenanceResponse.data;
    const hasOrg1Maintenance = user2Maintenance.some(req => req.id === maintenance1Response.data.id);
    
    if (hasOrg1Maintenance) {
      console.log('❌ Maintenance isolation FAILED - User 2 can see Org 1 maintenance');
    } else {
      console.log('✅ Maintenance isolation WORKING - User 2 can only see Org 2 maintenance');
      console.log(`   User 2 sees ${user2Maintenance.length} maintenance request(s)`);
    }

    // Test Permission Levels - Regular user should NOT be able to delete houses
    console.log('\n🔟 Testing Permission Levels...');
    
    // First, let's check the user's role
    const user2Profile = await axios.get(`${API_BASE}/auth/profile`, {
      headers: { Authorization: `Bearer ${userToken}` }
    });
    console.log('   User 2 role:', user2Profile.data.role);
    
    // Try to delete house as regular user (should fail if not admin)
    if (user2Profile.data.role !== 'admin') {
      try {
        await axios.delete(`${API_BASE}/houses/${house2Id}`, {
          headers: { Authorization: `Bearer ${userToken}` }
        });
        console.log('❌ Permission check FAILED - Regular user can delete houses');
      } catch (error) {
        if (error.response?.status === 403) {
          console.log('✅ Permission check WORKING - Regular user cannot delete houses');
        } else {
          console.log('⚠️ Unexpected error:', error.response?.status);
        }
      }
    } else {
      console.log('ℹ️ User 2 is admin, skipping permission test');
    }

    console.log('\n🎉 Security and Permissions Testing Complete!');
    console.log('\n📊 Test Summary:');
    console.log(`- Organizations created: 2`);
    console.log(`- Houses created: 2 (1 per org)`);
    console.log(`- Maintenance requests: 2 (1 per org)`);
    console.log(`- Data isolation: ✅ Working`);
    console.log(`- Cross-org access: ✅ Blocked`);
    console.log(`- Maintenance isolation: ✅ Working`);

  } catch (error) {
    console.error('❌ Security test failed:', error.response?.data || error.message);
  }
}

testPermissionsAndSecurity();
