const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testQuotesAndGratitude() {
  console.log('🧪 Testing Recovery Connect Quotes & Gratitude Features\n');
  
  let authToken = '';
  let quoteId = '';
  let gratitudeId = '';

  try {
    const timestamp = Date.now().toString().slice(-6);

    // Step 1: Register and login user
    console.log('1️⃣ Setting up test user...');
    const userEmail = `testuser${timestamp}@example.com`;
    await axios.post(`${API_BASE}/auth/register`, {
      email: userEmail,
      username: `testuser${timestamp}`,
      password: 'test123',
      firstName: 'Test',
      lastName: 'User'
    });

    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: userEmail,
      password: 'test123'
    });
    authToken = loginResponse.data.access_token;
    console.log('✅ User authenticated');

    // Step 2: Test Quotes API
    console.log('\n2️⃣ Testing Quotes API...');
    
    // Create quotes
    const quote1 = await axios.post(`${API_BASE}/quotes`, {
      text: 'One day at a time, one step at a time.',
      tags: 'recovery, motivation, daily'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    quoteId = quote1.data.id;
    console.log('✅ Created quote 1:', quote1.data.text);

    const quote2 = await axios.post(`${API_BASE}/quotes`, {
      text: 'Progress, not perfection.',
      tags: 'progress, mindset'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Created quote 2:', quote2.data.text);

    const quote3 = await axios.post(`${API_BASE}/quotes`, {
      text: 'You are stronger than you think.',
      tags: 'strength, self-belief'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Created quote 3:', quote3.data.text);

    // Get all quotes
    const allQuotes = await axios.get(`${API_BASE}/quotes`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Retrieved all quotes:', allQuotes.data.length, 'quotes');

    // Search quotes
    const searchResults = await axios.get(`${API_BASE}/quotes?search=progress`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Search results for "progress":', searchResults.data.length, 'quotes');

    // Filter by tags
    const tagResults = await axios.get(`${API_BASE}/quotes?tags=recovery`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Tag filter results for "recovery":', tagResults.data.length, 'quotes');

    // Get random quote
    const randomQuote = await axios.get(`${API_BASE}/quotes/random`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Random quote:', randomQuote.data ? 'Retrieved' : 'None available');

    // Update quote
    const updatedQuote = await axios.patch(`${API_BASE}/quotes/${quoteId}`, {
      text: 'One day at a time, one step at a time. You got this!',
      tags: 'recovery, motivation, daily, encouragement'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Updated quote:', updatedQuote.data.text);

    // Step 3: Test Gratitude API
    console.log('\n3️⃣ Testing Gratitude API...');

    // Create gratitude entries
    const gratitude1 = await axios.post(`${API_BASE}/gratitude`, {
      text: 'I am grateful for my family\'s support in my recovery journey.'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    gratitudeId = gratitude1.data.id;
    console.log('✅ Created gratitude entry 1:', gratitude1.data.text);

    const gratitude2 = await axios.post(`${API_BASE}/gratitude`, {
      text: 'I am grateful for having a safe place to live.'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Created gratitude entry 2:', gratitude2.data.text);

    const gratitude3 = await axios.post(`${API_BASE}/gratitude`, {
      text: 'I am grateful for the strength to face each new day.'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Created gratitude entry 3:', gratitude3.data.text);

    // Get all gratitude entries
    const allGratitude = await axios.get(`${API_BASE}/gratitude`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Retrieved all gratitude entries:', allGratitude.data.length, 'entries');

    // Get recent gratitude entries
    const recentGratitude = await axios.get(`${API_BASE}/gratitude/recent?days=7`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Retrieved recent gratitude entries (7 days):', recentGratitude.data.length, 'entries');

    // Get gratitude stats
    const gratitudeStats = await axios.get(`${API_BASE}/gratitude/stats`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Gratitude stats:', gratitudeStats.data);

    // Step 4: Test Data Isolation
    console.log('\n4️⃣ Testing data isolation...');

    // Create second user
    const user2Email = `testuser2${timestamp}@example.com`;
    await axios.post(`${API_BASE}/auth/register`, {
      email: user2Email,
      username: `testuser2${timestamp}`,
      password: 'test123',
      firstName: 'Test2',
      lastName: 'User2'
    });

    const login2Response = await axios.post(`${API_BASE}/auth/login`, {
      email: user2Email,
      password: 'test123'
    });
    const authToken2 = login2Response.data.access_token;

    // User 2 should not see User 1's quotes
    const user2Quotes = await axios.get(`${API_BASE}/quotes`, {
      headers: { Authorization: `Bearer ${authToken2}` }
    });
    console.log('✅ User 2 quotes (should be 0):', user2Quotes.data.length);

    // User 2 should not see User 1's gratitude
    const user2Gratitude = await axios.get(`${API_BASE}/gratitude`, {
      headers: { Authorization: `Bearer ${authToken2}` }
    });
    console.log('✅ User 2 gratitude entries (should be 0):', user2Gratitude.data.length);

    // Step 5: Test deletion
    console.log('\n5️⃣ Testing deletion...');

    // Delete a quote
    await axios.delete(`${API_BASE}/quotes/${quoteId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Deleted quote');

    // Delete a gratitude entry
    await axios.delete(`${API_BASE}/gratitude/${gratitudeId}`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Deleted gratitude entry');

    // Verify counts after deletion
    const finalQuotes = await axios.get(`${API_BASE}/quotes`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Final quote count:', finalQuotes.data.length);

    const finalGratitude = await axios.get(`${API_BASE}/gratitude`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Final gratitude count:', finalGratitude.data.length);

    // Step 6: Test error handling
    console.log('\n6️⃣ Testing error handling...');

    // Try to access another user's quote
    try {
      await axios.get(`${API_BASE}/quotes/${quote2.data.id}`, {
        headers: { Authorization: `Bearer ${authToken2}` }
      });
      console.log('❌ Security issue: User 2 can access User 1\'s quote');
    } catch (error) {
      console.log('✅ Security working: User 2 cannot access User 1\'s quote');
    }

    // Try to delete another user's gratitude
    try {
      await axios.delete(`${API_BASE}/gratitude/${gratitude2.data.id}`, {
        headers: { Authorization: `Bearer ${authToken2}` }
      });
      console.log('❌ Security issue: User 2 can delete User 1\'s gratitude');
    } catch (error) {
      console.log('✅ Security working: User 2 cannot delete User 1\'s gratitude');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log('- Quotes CRUD: ✅ Working');
    console.log('- Quotes search & filtering: ✅ Working');
    console.log('- Random quote: ✅ Working');
    console.log('- Gratitude CRUD: ✅ Working');
    console.log('- Gratitude stats: ✅ Working');
    console.log('- Data isolation: ✅ Working');
    console.log('- Security controls: ✅ Working');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testQuotesAndGratitude();
