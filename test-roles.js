const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testRoleBasedPermissions() {
  console.log('👥 Testing Role-Based Permissions\n');
  
  let adminToken = '';
  let regularUserToken = '';
  let organizationId = '';
  let houseId = '';

  try {
    // Create Admin User
    console.log('1️⃣ Creating Admin User...');
    const timestamp = Date.now().toString().slice(-6);
    const adminResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: `admin${timestamp}@roletest.com`,
      username: `admin${timestamp}`,
      password: 'password123',
      firstName: 'Admin',
      lastName: 'User'
    });
    
    const adminLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: adminResponse.data.user.email,
      password: 'password123'
    });
    adminToken = adminLogin.data.access_token;
    console.log('✅ Admin user created and logged in');

    // Create Organization
    console.log('\n2️⃣ Creating Organization...');
    const orgResponse = await axios.post(`${API_BASE}/organizations`, {
      name: 'Role Test Organization',
      description: 'Testing role-based permissions'
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    organizationId = orgResponse.data.id;
    console.log('✅ Organization created:', orgResponse.data.name);

    // Verify admin role
    const adminProfile = await axios.get(`${API_BASE}/auth/profile`, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    console.log('✅ Admin role confirmed:', adminProfile.data.role);

    // Create House (admin should be able to)
    console.log('\n3️⃣ Testing Admin House Creation...');
    const houseResponse = await axios.post(`${API_BASE}/houses`, {
      name: 'Admin Created House',
      address: '123 Admin St',
      capacity: 15
    }, {
      headers: { Authorization: `Bearer ${adminToken}` }
    });
    houseId = houseResponse.data.id;
    console.log('✅ Admin successfully created house:', houseResponse.data.name);

    // Create Regular User (not part of organization initially)
    console.log('\n4️⃣ Creating Regular User...');
    const userResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: `user${timestamp}@roletest.com`,
      username: `user${timestamp}`,
      password: 'password123',
      firstName: 'Regular',
      lastName: 'User'
    });
    
    const userLogin = await axios.post(`${API_BASE}/auth/login`, {
      email: userResponse.data.user.email,
      password: 'password123'
    });
    regularUserToken = userLogin.data.access_token;
    console.log('✅ Regular user created and logged in');

    // Check regular user's initial state
    const userProfile = await axios.get(`${API_BASE}/auth/profile`, {
      headers: { Authorization: `Bearer ${regularUserToken}` }
    });
    console.log('✅ Regular user role:', userProfile.data.role || 'user');
    console.log('✅ Regular user organization:', userProfile.data.organizationId || 'none');

    // Test: Regular user without organization should NOT be able to create houses
    console.log('\n5️⃣ Testing Regular User Permissions (No Org)...');
    try {
      await axios.post(`${API_BASE}/houses`, {
        name: 'Unauthorized House',
        address: '456 Unauthorized St',
        capacity: 10
      }, {
        headers: { Authorization: `Bearer ${regularUserToken}` }
      });
      console.log('❌ Permission test FAILED - Regular user without org can create houses');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Permission test PASSED - Regular user without org cannot create houses');
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data?.message);
      }
    }

    // Test: Regular user should NOT be able to access houses without organization
    console.log('\n6️⃣ Testing House Access Without Organization...');
    try {
      await axios.get(`${API_BASE}/houses`, {
        headers: { Authorization: `Bearer ${regularUserToken}` }
      });
      console.log('❌ Access test FAILED - Regular user without org can access houses');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Access test PASSED - Regular user without org cannot access houses');
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data?.message);
      }
    }

    // Test: Regular user should NOT be able to create maintenance requests without organization
    console.log('\n7️⃣ Testing Maintenance Access Without Organization...');
    try {
      await axios.post(`${API_BASE}/maintenance`, {
        title: 'Unauthorized Maintenance',
        description: 'Should not be allowed',
        priority: 'low',
        houseId: houseId
      }, {
        headers: { Authorization: `Bearer ${regularUserToken}` }
      });
      console.log('❌ Maintenance test FAILED - Regular user without org can create maintenance');
    } catch (error) {
      if (error.response?.status === 403 || error.response?.status === 404) {
        console.log('✅ Maintenance test PASSED - Regular user without org cannot create maintenance');
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data?.message);
      }
    }

    // Test: Admin should be able to update organization
    console.log('\n8️⃣ Testing Admin Organization Update...');
    try {
      const updateResponse = await axios.patch(`${API_BASE}/organizations/${organizationId}`, {
        description: 'Updated by admin'
      }, {
        headers: { Authorization: `Bearer ${adminToken}` }
      });
      console.log('✅ Admin successfully updated organization');
    } catch (error) {
      console.log('❌ Admin update FAILED:', error.response?.data?.message);
    }

    // Test: Regular user should NOT be able to update organization they don't belong to
    console.log('\n9️⃣ Testing Unauthorized Organization Update...');
    try {
      await axios.patch(`${API_BASE}/organizations/${organizationId}`, {
        description: 'Unauthorized update'
      }, {
        headers: { Authorization: `Bearer ${regularUserToken}` }
      });
      console.log('❌ Security FAILED - Regular user can update organization');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ Security PASSED - Regular user cannot update organization');
      } else {
        console.log('⚠️ Unexpected error:', error.response?.status, error.response?.data?.message);
      }
    }

    console.log('\n🎉 Role-Based Permission Testing Complete!');
    console.log('\n📊 Test Results:');
    console.log('- ✅ Admin can create organizations');
    console.log('- ✅ Admin can create houses');
    console.log('- ✅ Admin can update organizations');
    console.log('- ✅ Regular users without org cannot create houses');
    console.log('- ✅ Regular users without org cannot access houses');
    console.log('- ✅ Regular users without org cannot create maintenance');
    console.log('- ✅ Regular users cannot update organizations they don\'t belong to');

  } catch (error) {
    console.error('❌ Role test failed:', error.response?.data || error.message);
  }
}

testRoleBasedPermissions();
