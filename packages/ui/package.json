{"name": "@recovery-connect/ui", "version": "1.0.0", "description": "Shared UI components for Recovery Connect", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"react": "19.0.0", "react-native": "^0.79.3", "tailwindcss": "^3.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "@radix-ui/react-slot": "^1.0.2"}, "devDependencies": {"@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=18.0.0", "react-native": ">=0.70.0"}, "keywords": ["ui", "components", "react", "react-native", "tailwind"], "author": "<PERSON>", "license": "ISC"}