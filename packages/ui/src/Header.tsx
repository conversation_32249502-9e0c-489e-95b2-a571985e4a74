import * as React from "react"
import { cn } from "./utils/cn"

export interface HeaderProps extends React.HTMLAttributes<HTMLElement> {
  title: string
  subtitle?: string
  actions?: React.ReactNode
  showBack?: boolean
  onBack?: () => void
}

const Header = React.forwardRef<HTMLElement, HeaderProps>(
  ({ className, title, subtitle, actions, showBack, onBack, ...props }, ref) => {
    return (
      <header
        ref={ref}
        className={cn(
          "flex items-center justify-between border-b bg-background px-4 py-3 sm:px-6",
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-4">
          {showBack && (
            <button
              onClick={onBack}
              className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-accent hover:text-accent-foreground"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          )}
          <div>
            <h1 className="text-xl font-semibold text-foreground">{title}</h1>
            {subtitle && (
              <p className="text-sm text-muted-foreground">{subtitle}</p>
            )}
          </div>
        </div>
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </header>
    )
  }
)
Header.displayName = "Header"

export { Header }
