import * as React from "react"
import { cn } from "./utils/cn"

export interface CleanTimeCounterProps {
  startDate: Date
  className?: string
  showDetails?: boolean
}

interface TimeUnit {
  value: number
  label: string
  shortLabel: string
}

const CleanTimeCounter: React.FC<CleanTimeCounterProps> = ({
  startDate,
  className,
  showDetails = true
}) => {
  const [timeUnits, setTimeUnits] = React.useState<TimeUnit[]>([])

  React.useEffect(() => {
    const calculateTime = () => {
      const now = new Date()
      const diff = now.getTime() - startDate.getTime()
      
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      
      const years = Math.floor(days / 365)
      const months = Math.floor((days % 365) / 30)
      const remainingDays = days % 30

      setTimeUnits([
        { value: years, label: "years", shortLabel: "y" },
        { value: months, label: "months", shortLabel: "m" },
        { value: remainingDays, label: "days", shortLabel: "d" },
        { value: hours, label: "hours", shortLabel: "h" },
        { value: minutes, label: "minutes", shortLabel: "min" }
      ])
    }

    calculateTime()
    const interval = setInterval(calculateTime, 60000) // Update every minute

    return () => clearInterval(interval)
  }, [startDate])

  const totalDays = Math.floor((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))

  return (
    <div className={cn("text-center space-y-4", className)}>
      {/* Main Counter */}
      <div className="space-y-2">
        <div className="text-4xl font-bold text-primary">
          {totalDays}
        </div>
        <div className="text-lg text-muted-foreground">
          {totalDays === 1 ? "Day" : "Days"} Clean
        </div>
      </div>

      {/* Detailed Breakdown */}
      {showDetails && (
        <div className="grid grid-cols-3 gap-4 text-sm">
          {timeUnits.slice(0, 3).map((unit, index) => (
            unit.value > 0 && (
              <div key={index} className="text-center">
                <div className="font-semibold text-foreground">{unit.value}</div>
                <div className="text-muted-foreground">{unit.label}</div>
              </div>
            )
          ))}
        </div>
      )}

      {/* Motivational Message */}
      <div className="text-sm text-muted-foreground">
        {totalDays === 0 && "Every journey begins with a single step"}
        {totalDays === 1 && "One day at a time 💪"}
        {totalDays >= 2 && totalDays < 7 && "Building momentum! 🌱"}
        {totalDays >= 7 && totalDays < 30 && "One week strong! 🔥"}
        {totalDays >= 30 && totalDays < 90 && "One month milestone! 🎉"}
        {totalDays >= 90 && totalDays < 365 && "90+ days of strength! 💎"}
        {totalDays >= 365 && "Over a year of recovery! 🏆"}
      </div>
    </div>
  )
}

export { CleanTimeCounter }
