import * as React from "react"
import { cn } from "./utils/cn"
import { But<PERSON> } from "./Button"

export interface PanicButtonProps {
  onPress: () => void
  className?: string
  disabled?: boolean
  size?: "default" | "large"
}

const PanicButton: React.FC<PanicButtonProps> = ({
  onPress,
  className,
  disabled = false,
  size = "default"
}) => {
  const [isPressed, setIsPressed] = React.useState(false)

  const handlePress = () => {
    if (disabled) return
    
    setIsPressed(true)
    onPress()
    
    // Reset the pressed state after animation
    setTimeout(() => setIsPressed(false), 200)
  }

  return (
    <Button
      variant="panic"
      size={size === "large" ? "panic" : "lg"}
      onClick={handlePress}
      disabled={disabled}
      className={cn(
        "relative overflow-hidden transition-all duration-200",
        "shadow-lg hover:shadow-xl",
        "active:scale-95",
        isPressed && "animate-pulse",
        size === "large" && "w-full max-w-xs mx-auto",
        className
      )}
    >
      <div className="flex items-center justify-center space-x-2">
        <svg
          className="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
          />
        </svg>
        <span>EMERGENCY</span>
      </div>
      
      {/* Ripple effect */}
      {isPressed && (
        <div className="absolute inset-0 bg-white/20 animate-ping rounded-md" />
      )}
    </Button>
  )
}

export { PanicButton }
