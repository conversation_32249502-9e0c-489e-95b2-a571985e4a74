import React, { useState } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  Animated,
} from 'react-native';

export interface PanicButtonProps {
  onPress: () => void;
  style?: ViewStyle;
  disabled?: boolean;
  size?: 'default' | 'large';
}

export function PanicButton({
  onPress,
  style,
  disabled = false,
  size = 'default',
}: PanicButtonProps) {
  const [isPressed, setIsPressed] = useState(false);
  const [scaleValue] = useState(new Animated.Value(1));

  const handlePressIn = () => {
    setIsPressed(true);
    Animated.spring(scaleValue, {
      toValue: 0.95,
      useNativeDriver: true,
    }).start();
  };

  const handlePressOut = () => {
    setIsPressed(false);
    Animated.spring(scaleValue, {
      toValue: 1,
      useNativeDriver: true,
    }).start();
  };

  const handlePress = () => {
    if (disabled) return;
    onPress();
  };

  const buttonStyle = [
    styles.button,
    size === 'large' ? styles.buttonLarge : styles.buttonDefault,
    disabled && styles.buttonDisabled,
    style,
  ];

  const textStyle = [
    styles.text,
    size === 'large' ? styles.textLarge : styles.textDefault,
    disabled && styles.textDisabled,
  ];

  return (
    <Animated.View style={{ transform: [{ scale: scaleValue }] }}>
      <TouchableOpacity
        style={buttonStyle}
        onPress={handlePress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.9}
      >
        <View style={styles.content}>
          <Text style={styles.icon}>⚠️</Text>
          <Text style={textStyle}>EMERGENCY</Text>
        </View>
        
        {/* Ripple effect */}
        {isPressed && (
          <View style={styles.ripple} />
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#dc2626',
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
    position: 'relative',
    overflow: 'hidden',
  },
  buttonDefault: {
    paddingHorizontal: 32,
    paddingVertical: 16,
    minHeight: 60,
  },
  buttonLarge: {
    paddingHorizontal: 48,
    paddingVertical: 24,
    minHeight: 80,
    width: '100%',
    maxWidth: 300,
  },
  buttonDisabled: {
    backgroundColor: '#9ca3af',
    opacity: 0.6,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    fontSize: 24,
    marginBottom: 4,
  },
  text: {
    color: '#ffffff',
    fontWeight: 'bold',
    textAlign: 'center',
    letterSpacing: 1,
  },
  textDefault: {
    fontSize: 16,
  },
  textLarge: {
    fontSize: 20,
  },
  textDisabled: {
    color: '#ffffff',
  },
  ripple: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
  },
});
