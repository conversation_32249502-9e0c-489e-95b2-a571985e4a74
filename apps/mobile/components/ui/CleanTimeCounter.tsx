import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';

export interface CleanTimeCounterProps {
  startDate: Date;
  style?: ViewStyle;
  showDetails?: boolean;
}

interface TimeUnit {
  value: number;
  label: string;
  shortLabel: string;
}

export function CleanTimeCounter({
  startDate,
  style,
  showDetails = true,
}: CleanTimeCounterProps) {
  const [timeUnits, setTimeUnits] = useState<TimeUnit[]>([]);

  useEffect(() => {
    const calculateTime = () => {
      const now = new Date();
      const diff = now.getTime() - startDate.getTime();
      
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      
      const years = Math.floor(days / 365);
      const months = Math.floor((days % 365) / 30);
      const remainingDays = days % 30;

      setTimeUnits([
        { value: years, label: 'years', shortLabel: 'y' },
        { value: months, label: 'months', shortLabel: 'm' },
        { value: remainingDays, label: 'days', shortLabel: 'd' },
        { value: hours, label: 'hours', shortLabel: 'h' },
        { value: minutes, label: 'minutes', shortLabel: 'min' },
      ]);
    };

    calculateTime();
    const interval = setInterval(calculateTime, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [startDate]);

  const totalDays = Math.floor((new Date().getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  const getMotivationalMessage = () => {
    if (totalDays === 0) return 'Every journey begins with a single step';
    if (totalDays === 1) return 'One day at a time 💪';
    if (totalDays >= 2 && totalDays < 7) return 'Building momentum! 🌱';
    if (totalDays >= 7 && totalDays < 30) return 'One week strong! 🔥';
    if (totalDays >= 30 && totalDays < 90) return 'One month milestone! 🎉';
    if (totalDays >= 90 && totalDays < 365) return '90+ days of strength! 💎';
    return 'Over a year of recovery! 🏆';
  };

  return (
    <View style={[styles.container, style]}>
      {/* Main Counter */}
      <View style={styles.mainCounter}>
        <Text style={styles.daysNumber}>{totalDays}</Text>
        <Text style={styles.daysLabel}>
          {totalDays === 1 ? 'Day' : 'Days'} Clean
        </Text>
      </View>

      {/* Detailed Breakdown */}
      {showDetails && (
        <View style={styles.breakdown}>
          {timeUnits.slice(0, 3).map((unit, index) => (
            unit.value > 0 && (
              <View key={index} style={styles.timeUnit}>
                <Text style={styles.timeValue}>{unit.value}</Text>
                <Text style={styles.timeLabel}>{unit.label}</Text>
              </View>
            )
          ))}
        </View>
      )}

      {/* Motivational Message */}
      <Text style={styles.motivationalMessage}>
        {getMotivationalMessage()}
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 20,
  },
  mainCounter: {
    alignItems: 'center',
    marginBottom: 16,
  },
  daysNumber: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  daysLabel: {
    fontSize: 18,
    color: '#64748b',
    fontWeight: '500',
  },
  breakdown: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 20,
    marginBottom: 16,
  },
  timeUnit: {
    alignItems: 'center',
  },
  timeValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  timeLabel: {
    fontSize: 12,
    color: '#64748b',
    marginTop: 2,
  },
  motivationalMessage: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
