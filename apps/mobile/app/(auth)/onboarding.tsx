import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { cleanTimeApi } from '@/lib/api';

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(0);
  const [sobrietyDate, setSobrietyDate] = useState('');
  const [notes, setNotes] = useState('');

  const steps = [
    {
      title: 'Welcome to Recovery Connect',
      subtitle: 'Your journey to recovery starts here',
      content: (
        <View style={styles.welcomeContent}>
          <Text style={styles.welcomeText}>
            Recovery Connect is designed to support you every step of the way. 
            Track your progress, journal your thoughts, and stay connected with your support network.
          </Text>
          <View style={styles.featureList}>
            <Text style={styles.feature}>📊 Track your clean time</Text>
            <Text style={styles.feature}>📝 Private encrypted journaling</Text>
            <Text style={styles.feature}>📅 Manage appointments and meetings</Text>
            <Text style={styles.feature}>🚨 Emergency support contacts</Text>
          </View>
        </View>
      ),
    },
    {
      title: 'Set Your Sobriety Date',
      subtitle: 'When did your recovery journey begin?',
      content: (
        <View style={styles.dateContent}>
          <Text style={styles.dateDescription}>
            Setting your sobriety date helps track your progress and celebrate milestones. 
            You can always update this later in your profile.
          </Text>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Sobriety Start Date</Text>
            <TextInput
              style={styles.dateInput}
              value={sobrietyDate}
              onChangeText={setSobrietyDate}
              placeholder="YYYY-MM-DD"
              placeholderTextColor="#9ca3af"
            />
            <Text style={styles.inputHint}>Format: YYYY-MM-DD (e.g., 2024-01-15)</Text>
          </View>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Notes (Optional)</Text>
            <TextInput
              style={styles.notesInput}
              value={notes}
              onChangeText={setNotes}
              placeholder="Add any notes about your recovery journey..."
              placeholderTextColor="#9ca3af"
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>
      ),
    },
    {
      title: 'You\'re All Set!',
      subtitle: 'Ready to begin your recovery journey',
      content: (
        <View style={styles.completeContent}>
          <Text style={styles.completeText}>
            🎉 Congratulations! You've successfully set up Recovery Connect.
          </Text>
          <Text style={styles.completeDescription}>
            Remember, recovery is a journey, not a destination. Take it one day at a time, 
            and don't hesitate to reach out for support when you need it.
          </Text>
          <View style={styles.nextSteps}>
            <Text style={styles.nextStepsTitle}>What's next?</Text>
            <Text style={styles.nextStep}>• Explore your dashboard</Text>
            <Text style={styles.nextStep}>• Write your first journal entry</Text>
            <Text style={styles.nextStep}>• Add emergency contacts</Text>
            <Text style={styles.nextStep}>• Schedule your first meeting</Text>
          </View>
        </View>
      ),
    },
  ];

  const handleNext = async () => {
    if (currentStep === 1) {
      // Validate and save sobriety date
      if (sobrietyDate) {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(sobrietyDate)) {
          Alert.alert('Invalid Date', 'Please enter date in YYYY-MM-DD format');
          return;
        }
        
        const date = new Date(sobrietyDate);
        if (isNaN(date.getTime()) || date > new Date()) {
          Alert.alert('Invalid Date', 'Please enter a valid date that is not in the future');
          return;
        }

        try {
          await cleanTimeApi.create({
            startDate: sobrietyDate,
            notes: notes || undefined,
          });
        } catch (error) {
          console.error('Failed to save sobriety date:', error);
          Alert.alert('Error', 'Failed to save sobriety date. You can set it up later in your profile.');
        }
      }
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete onboarding
      router.replace('/(tabs)');
    }
  };

  const handleSkip = () => {
    if (currentStep === 1) {
      // Skip sobriety date setup
      setCurrentStep(currentStep + 1);
    } else {
      router.replace('/(tabs)');
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const currentStepData = steps[currentStep];

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.progressContainer}>
          {steps.map((_, index) => (
            <View
              key={index}
              style={[
                styles.progressDot,
                index <= currentStep ? styles.progressDotActive : styles.progressDotInactive,
              ]}
            />
          ))}
        </View>
        <Text style={styles.stepIndicator}>
          {currentStep + 1} of {steps.length}
        </Text>
      </View>

      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        <Text style={styles.title}>{currentStepData.title}</Text>
        <Text style={styles.subtitle}>{currentStepData.subtitle}</Text>
        {currentStepData.content}
      </ScrollView>

      <View style={styles.footer}>
        {currentStep > 0 && (
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Text style={styles.backButtonText}>Back</Text>
          </TouchableOpacity>
        )}
        
        <View style={styles.footerButtons}>
          {currentStep === 1 && (
            <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
              <Text style={styles.skipButtonText}>Skip for now</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
            <Text style={styles.nextButtonText}>
              {currentStep === steps.length - 1 ? 'Get Started' : 'Next'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    padding: 24,
    paddingBottom: 16,
    alignItems: 'center',
  },
  progressContainer: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  progressDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  progressDotActive: {
    backgroundColor: '#3b82f6',
  },
  progressDotInactive: {
    backgroundColor: '#d1d5db',
  },
  stepIndicator: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 24,
    paddingTop: 0,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 32,
  },
  welcomeContent: {
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  featureList: {
    alignSelf: 'stretch',
  },
  feature: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 12,
    paddingLeft: 8,
  },
  dateContent: {
    alignSelf: 'stretch',
  },
  dateDescription: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  dateInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#374151',
  },
  inputHint: {
    fontSize: 14,
    color: '#64748b',
    marginTop: 4,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#374151',
    minHeight: 100,
  },
  completeContent: {
    alignItems: 'center',
  },
  completeText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 16,
  },
  completeDescription: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    textAlign: 'center',
    marginBottom: 32,
  },
  nextSteps: {
    alignSelf: 'stretch',
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 20,
  },
  nextStepsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },
  nextStep: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 8,
  },
  footer: {
    padding: 24,
    paddingTop: 16,
  },
  footerButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  backButton: {
    alignSelf: 'flex-start',
    marginBottom: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#64748b',
    fontWeight: '500',
  },
  skipButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  skipButtonText: {
    fontSize: 16,
    color: '#64748b',
    fontWeight: '500',
  },
  nextButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ffffff',
  },
});
