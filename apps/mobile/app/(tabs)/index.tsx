import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@/contexts/AuthContext';
import { cleanTimeApi, quotesApi } from '@/lib/api';
import { CleanTimeCounter } from '@/components/ui';

interface CleanTimeData {
  id: string;
  startDate: string;
  notes?: string;
}

interface Quote {
  id: string;
  text: string;
  tags?: string;
}

export default function DashboardScreen() {
  const { user } = useAuth();
  const [cleanTimeData, setCleanTimeData] = useState<CleanTimeData | null>(null);
  const [randomQuote, setRandomQuote] = useState<Quote | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [cleanTime, quote] = await Promise.all([
        cleanTimeApi.getCurrent().catch(() => null),
        quotesApi.getRandom().catch(() => null),
      ]);
      setCleanTimeData(cleanTime);
      setRandomQuote(quote);
    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const calculateDays = () => {
    if (!cleanTimeData?.startDate) return 0;
    const startDate = new Date(cleanTimeData.startDate);
    const today = new Date();
    return Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  };

  const getMotivationalMessage = (days: number) => {
    if (days === 0) return "Every journey begins with a single step 🌟";
    if (days === 1) return "One day at a time 💪";
    if (days >= 2 && days < 7) return "Building momentum! 🌱";
    if (days >= 7 && days < 30) return "One week strong! 🔥";
    if (days >= 30 && days < 90) return "One month milestone! 🎉";
    if (days >= 90 && days < 365) return "90+ days of strength! 💎";
    return "Over a year of recovery! 🏆";
  };

  const handleSetupCleanTime = () => {
    Alert.alert(
      'Setup Clean Time',
      'Would you like to set your sobriety start date?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Setup', onPress: () => {/* Navigate to setup */} },
      ]
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const cleanDays = calculateDays();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={styles.header}>
          <Text style={styles.greeting}>
            Welcome back, {user?.firstName || user?.username || 'Friend'}!
          </Text>
          <Text style={styles.date}>{new Date().toLocaleDateString()}</Text>
        </View>

        {/* Clean Time Counter */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Clean Time</Text>
          {cleanTimeData ? (
            <CleanTimeCounter
              startDate={new Date(cleanTimeData.startDate)}
              showDetails={true}
            />
          ) : (
            <View style={styles.setupContainer}>
              <Text style={styles.setupText}>Track your recovery journey</Text>
              <TouchableOpacity style={styles.setupButton} onPress={handleSetupCleanTime}>
                <Text style={styles.setupButtonText}>Setup Clean Time</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

        {/* Daily Quote */}
        {randomQuote && (
          <View style={styles.card}>
            <Text style={styles.cardTitle}>Daily Inspiration</Text>
            <Text style={styles.quoteText}>"{randomQuote.text}"</Text>
          </View>
        )}

        {/* Quick Actions */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonText}>📝 New Journal Entry</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Text style={styles.actionButtonText}>📅 Add Event</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 24,
    paddingBottom: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 4,
  },
  date: {
    fontSize: 16,
    color: '#64748b',
  },
  card: {
    backgroundColor: '#ffffff',
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 16,
  },

  setupContainer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  setupText: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 16,
    textAlign: 'center',
  },
  setupButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  setupButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  quoteText: {
    fontSize: 16,
    color: '#374151',
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 24,
  },
  quickActions: {
    gap: 12,
  },
  actionButton: {
    backgroundColor: '#f1f5f9',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
});
