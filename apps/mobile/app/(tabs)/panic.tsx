import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
  RefreshControl,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { panicApi } from '@/lib/api';
import { PanicButton } from '@/components/ui';

interface PanicContact {
  id: string;
  name: string;
  phoneNumber: string;
  relationship?: string;
  isPrimary: boolean;
}

export default function PanicScreen() {
  const [contacts, setContacts] = useState<PanicContact[]>([]);
  const [primaryContact, setPrimaryContact] = useState<PanicContact | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchContacts();
  }, []);

  const fetchContacts = async () => {
    try {
      const [allContacts, primary] = await Promise.all([
        panicApi.getContacts(),
        panicApi.getPrimary().catch(() => null),
      ]);
      setContacts(allContacts);
      setPrimaryContact(primary);
    } catch (error) {
      console.error('Failed to fetch panic contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchContacts();
    setRefreshing(false);
  };

  const handleEmergencyPress = () => {
    Alert.alert(
      'Emergency Help',
      'What type of help do you need?',
      [
        {
          text: 'Call 911',
          onPress: () => callNumber('911'),
          style: 'destructive',
        },
        {
          text: 'Crisis Hotline',
          onPress: () => callNumber('988'),
        },
        {
          text: 'Primary Contact',
          onPress: () => {
            if (primaryContact) {
              callNumber(primaryContact.phoneNumber);
            } else {
              Alert.alert('No Primary Contact', 'Please add a primary contact first.');
            }
          },
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const callNumber = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device');
        }
      })
      .catch((error) => {
        console.error('Error making phone call:', error);
        Alert.alert('Error', 'Failed to make phone call');
      });
  };

  const handleContactPress = (contact: PanicContact) => {
    Alert.alert(
      contact.name,
      `Call ${contact.name}?`,
      [
        {
          text: 'Call',
          onPress: () => callNumber(contact.phoneNumber),
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  const handleManageContacts = () => {
    router.push('/panic/contacts');
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading emergency contacts...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Emergency Help</Text>
          <Text style={styles.subtitle}>
            Get immediate support when you need it most
          </Text>
        </View>

        {/* Emergency Button */}
        <View style={styles.emergencySection}>
          <PanicButton
            onPress={handleEmergencyPress}
            size="large"
          />
          
          {primaryContact && (
            <View style={styles.primaryContactInfo}>
              <Text style={styles.primaryContactText}>
                Primary contact: {primaryContact.name}
              </Text>
              <Text style={styles.primaryContactPhone}>
                {primaryContact.phoneNumber}
              </Text>
            </View>
          )}
        </View>

        {/* Quick Access Numbers */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Crisis Resources</Text>
          <View style={styles.resourcesContainer}>
            <TouchableOpacity
              style={styles.resourceButton}
              onPress={() => callNumber('988')}
            >
              <Text style={styles.resourceIcon}>📞</Text>
              <View style={styles.resourceInfo}>
                <Text style={styles.resourceTitle}>Crisis Lifeline</Text>
                <Text style={styles.resourceSubtitle}>988 - 24/7 Support</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.resourceButton}
              onPress={() => callNumber('911')}
            >
              <Text style={styles.resourceIcon}>🚨</Text>
              <View style={styles.resourceInfo}>
                <Text style={styles.resourceTitle}>Emergency Services</Text>
                <Text style={styles.resourceSubtitle}>911 - Immediate Help</Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Personal Contacts */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Support Network</Text>
            <TouchableOpacity
              style={styles.manageButton}
              onPress={handleManageContacts}
            >
              <Text style={styles.manageButtonText}>Manage</Text>
            </TouchableOpacity>
          </View>

          {contacts.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyTitle}>No Contacts Added</Text>
              <Text style={styles.emptyText}>
                Add trusted contacts who can support you during difficult times.
              </Text>
              <TouchableOpacity
                style={styles.emptyButton}
                onPress={handleManageContacts}
              >
                <Text style={styles.emptyButtonText}>Add Contacts</Text>
              </TouchableOpacity>
            </View>
          ) : (
            <View style={styles.contactsContainer}>
              {contacts.map((contact) => (
                <TouchableOpacity
                  key={contact.id}
                  style={styles.contactCard}
                  onPress={() => handleContactPress(contact)}
                >
                  <View style={styles.contactInfo}>
                    <Text style={styles.contactName}>
                      {contact.name}
                      {contact.isPrimary && (
                        <Text style={styles.primaryBadge}> • Primary</Text>
                      )}
                    </Text>
                    <Text style={styles.contactPhone}>{contact.phoneNumber}</Text>
                    {contact.relationship && (
                      <Text style={styles.contactRelationship}>
                        {contact.relationship}
                      </Text>
                    )}
                  </View>
                  <Text style={styles.callIcon}>📞</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>

        {/* Safety Tips */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Remember</Text>
          <View style={styles.tipsContainer}>
            <Text style={styles.tip}>• You are not alone in this journey</Text>
            <Text style={styles.tip}>• It's okay to ask for help</Text>
            <Text style={styles.tip}>• This feeling will pass</Text>
            <Text style={styles.tip}>• You have overcome challenges before</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#64748b',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 24,
    paddingBottom: 16,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1e293b',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  emergencySection: {
    padding: 24,
    alignItems: 'center',
  },

  primaryContactInfo: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#fef2f2',
    borderRadius: 8,
    alignItems: 'center',
  },
  primaryContactText: {
    fontSize: 14,
    color: '#991b1b',
    fontWeight: '500',
  },
  primaryContactPhone: {
    fontSize: 14,
    color: '#991b1b',
    marginTop: 2,
  },
  section: {
    padding: 24,
    paddingTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#374151',
  },
  manageButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  manageButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  resourcesContainer: {
    gap: 12,
  },
  resourceButton: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  resourceIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  resourceInfo: {
    flex: 1,
  },
  resourceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  resourceSubtitle: {
    fontSize: 14,
    color: '#64748b',
  },
  emptyContainer: {
    alignItems: 'center',
    padding: 32,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
  },
  emptyButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  contactsContainer: {
    gap: 12,
  },
  contactCard: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  contactInfo: {
    flex: 1,
  },
  contactName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  primaryBadge: {
    fontSize: 14,
    color: '#dc2626',
    fontWeight: '500',
  },
  contactPhone: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 2,
  },
  contactRelationship: {
    fontSize: 12,
    color: '#64748b',
    fontStyle: 'italic',
  },
  callIcon: {
    fontSize: 20,
    marginLeft: 12,
  },
  tipsContainer: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tip: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
    marginBottom: 8,
  },
});
