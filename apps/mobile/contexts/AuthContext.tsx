import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage'
import { router } from 'expo-router'
import { authApi } from '../lib/api'
import LoadingScreen from '../components/LoadingScreen'

interface User {
  id: string
  email: string
  username: string
  firstName?: string
  lastName?: string
  sobrietyDate?: string
  createdAt: string
  organizationId?: string
  role: string
}

interface AuthContextType {
  user: User | null
  loading: boolean
  login: (email: string, password: string) => Promise<void>
  register: (data: {
    email: string
    username: string
    password: string
    firstName?: string
    lastName?: string
  }) => Promise<void>
  logout: () => Promise<void>
  isAuthenticated: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const token = await AsyncStorage.getItem('auth_token')
      if (token) {
        const userData = await authApi.getProfile()
        setUser(userData)
        // Navigate to main app if authenticated
        router.replace('/(tabs)')
      } else {
        // Navigate to auth if not authenticated
        router.replace('/(auth)/login')
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      await AsyncStorage.removeItem('auth_token')
      router.replace('/(auth)/login')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const response = await authApi.login({ email, password })
      const userData = await authApi.getProfile()
      setUser(userData)
      router.replace('/(tabs)')
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    }
  }

  const register = async (data: {
    email: string
    username: string
    password: string
    firstName?: string
    lastName?: string
  }) => {
    try {
      await authApi.register(data)
      // After registration, log the user in
      await login(data.email, data.password)
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with logout even if API call fails
    }
    setUser(null)
    await AsyncStorage.removeItem('auth_token')
    router.replace('/(auth)/login')
  }

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user,
  }

  if (loading) {
    return <LoadingScreen />
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
