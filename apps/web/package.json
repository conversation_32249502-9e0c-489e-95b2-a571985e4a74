{"name": "web", "version": "1.0.0", "description": "Recovery Connect Web App", "main": "index.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["recovery", "nextjs", "web"], "author": "<PERSON>", "license": "ISC", "packageManager": "pnpm@10.11.1", "dependencies": {"next": "^15.3.3", "react": "19.0.0", "react-dom": "19.0.0", "@recovery-connect/ui": "workspace:*", "next-auth": "^4.24.5", "axios": "^1.6.2", "date-fns": "^3.0.6", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "devDependencies": {"@types/node": "^22.15.29", "@types/react": "~19.0.10", "typescript": "^5.8.3", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}}