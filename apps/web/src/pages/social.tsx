'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarInitials } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Heart, MessageCircle, Share, MoreHorizontal, Send, Flag } from 'lucide-react'
import { socialApi, moderationApi } from '@/lib/api'
import { toast } from 'sonner'
import { useAuth } from '@/hooks/useAuth'

interface Post {
  id: string
  content: string
  mediaUrls: string[]
  postType: string
  isAnonymous: boolean
  createdAt: string
  user: {
    id: string
    username: string
    firstName: string
    lastName: string
  }
  reactions: Array<{
    id: string
    reactionType: string
    user: {
      id: string
      username: string
      firstName: string
    }
  }>
  userReaction: string | null
  _count: {
    reactions: number
  }
}

const REACTION_TYPES = [
  { type: 'LIKE', emoji: '👍', label: 'Like' },
  { type: 'HEART', emoji: '❤️', label: 'Heart' },
  { type: 'SUPPORT', emoji: '🤗', label: 'Support' },
  { type: 'CELEBRATE', emoji: '🎉', label: 'Celebrate' },
  { type: 'PRAY', emoji: '🙏', label: 'Pray' },
]

export default function SocialPage() {
  const { user } = useAuth()
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [newPostContent, setNewPostContent] = useState('')
  const [isPosting, setIsPosting] = useState(false)

  useEffect(() => {
    fetchPosts()
  }, [])

  const fetchPosts = async () => {
    try {
      setLoading(true)
      const feedData = await socialApi.getFeed()
      setPosts(feedData)
    } catch (error) {
      console.error('Failed to fetch posts:', error)
      toast.error('Failed to load social feed')
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePost = async () => {
    if (!newPostContent.trim()) return

    try {
      setIsPosting(true)
      await socialApi.createPost(newPostContent.trim())
      setNewPostContent('')
      toast.success('Post created successfully')
      fetchPosts() // Refresh the feed
    } catch (error) {
      console.error('Failed to create post:', error)
      toast.error('Failed to create post')
    } finally {
      setIsPosting(false)
    }
  }

  const handleReaction = async (postId: string, reactionType: string) => {
    try {
      await socialApi.reactToPost(postId, reactionType)
      fetchPosts() // Refresh to show updated reactions
    } catch (error) {
      console.error('Failed to react to post:', error)
      toast.error('Failed to react to post')
    }
  }

  const handleReport = async (postId: string) => {
    try {
      await moderationApi.createReport('POST', postId, 'INAPPROPRIATE_CONTENT', 'Reported from social feed')
      toast.success('Post reported successfully')
    } catch (error) {
      console.error('Failed to report post:', error)
      toast.error('Failed to report post')
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const now = new Date()
    const postDate = new Date(dateString)
    const diffInMinutes = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  const getReactionCount = (post: Post, reactionType: string) => {
    return post.reactions.filter(r => r.reactionType === reactionType).length
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading social feed...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">Community Feed</h1>
          <p className="text-gray-600 mt-1">Share your journey and support others</p>
        </div>

        {/* Create Post */}
        <Card>
          <CardHeader>
            <div className="flex items-center space-x-3">
              <Avatar>
                <AvatarFallback>
                  <AvatarInitials name={user?.firstName || user?.username || 'U'} />
                </AvatarFallback>
              </Avatar>
              <div>
                <p className="font-medium">{user?.firstName || user?.username}</p>
                <p className="text-sm text-gray-500">Share something with the community</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              placeholder="What's on your mind? Share your thoughts, progress, or ask for support..."
              value={newPostContent}
              onChange={(e) => setNewPostContent(e.target.value)}
              className="min-h-[100px] resize-none"
            />
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {newPostContent.length}/500 characters
              </div>
              <Button
                onClick={handleCreatePost}
                disabled={!newPostContent.trim() || isPosting || newPostContent.length > 500}
              >
                <Send className="h-4 w-4 mr-2" />
                {isPosting ? 'Posting...' : 'Post'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Posts Feed */}
        <div className="space-y-4">
          {posts.map((post) => (
            <Card key={post.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarFallback>
                        <AvatarInitials 
                          name={post.isAnonymous ? 'Anonymous' : (post.user.firstName || post.user.username)} 
                        />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">
                        {post.isAnonymous ? 'Anonymous' : (post.user.firstName || post.user.username)}
                      </p>
                      <p className="text-sm text-gray-500">{formatTimeAgo(post.createdAt)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline">{post.postType}</Badge>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleReport(post.id)}
                    >
                      <Flag className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-800 whitespace-pre-wrap">{post.content}</p>

                {/* Media */}
                {post.mediaUrls && post.mediaUrls.length > 0 && (
                  <div className="grid grid-cols-2 gap-2">
                    {post.mediaUrls.map((url, index) => (
                      <img
                        key={index}
                        src={url}
                        alt={`Post media ${index + 1}`}
                        className="rounded-lg object-cover w-full h-48"
                      />
                    ))}
                  </div>
                )}

                {/* Reactions */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {REACTION_TYPES.map((reaction) => {
                        const count = getReactionCount(post, reaction.type)
                        const isActive = post.userReaction === reaction.type
                        
                        return (
                          <Button
                            key={reaction.type}
                            variant={isActive ? "default" : "ghost"}
                            size="sm"
                            onClick={() => handleReaction(post.id, reaction.type)}
                            className="flex items-center space-x-1"
                          >
                            <span>{reaction.emoji}</span>
                            {count > 0 && <span className="text-xs">{count}</span>}
                          </Button>
                        )
                      })}
                    </div>
                    <div className="text-sm text-gray-500">
                      {post._count.reactions} reactions
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {posts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No posts yet</h3>
              <p>Be the first to share something with the community!</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
