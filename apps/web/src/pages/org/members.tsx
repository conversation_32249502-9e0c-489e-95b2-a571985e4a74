import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { organizationsApi } from '../../lib/api'
import { useIsOrgAdmin, useOrgId } from '../../hooks/useAuth'

interface User {
  id: string
  email: string
  username: string
  firstName?: string
  lastName?: string
  role: string
  createdAt: string
}

interface Organization {
  id: string
  name: string
  users: User[]
}

export default function OrgMembers() {
  const router = useRouter()
  const isAdmin = useIsOrgAdmin()
  const orgId = useOrgId()
  
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [loading, setLoading] = useState(true)
  const [inviteEmail, setInviteEmail] = useState('')
  const [inviteRole, setInviteRole] = useState('MEMBER')
  const [inviting, setInviting] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (!isAdmin) {
      router.push('/')
      return
    }
    
    if (!orgId) {
      router.push('/onboarding')
      return
    }

    fetchOrganization()
  }, [isAdmin, orgId, router])

  const fetchOrganization = async () => {
    try {
      const org = await organizationsApi.getById(orgId!)
      setOrganization(org)
    } catch (error) {
      console.error('Failed to fetch organization:', error)
      setError('Failed to load organization data')
    } finally {
      setLoading(false)
    }
  }

  const handleInviteUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!orgId || !inviteEmail.trim()) return

    setInviting(true)
    setError('')

    try {
      await organizationsApi.inviteUser(orgId, inviteEmail.trim(), inviteRole)
      setInviteEmail('')
      setInviteRole('MEMBER')
      await fetchOrganization() // Refresh the list
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to invite user')
    } finally {
      setInviting(false)
    }
  }

  const handleRemoveUser = async (userId: string) => {
    if (!orgId || !confirm('Are you sure you want to remove this user from the organization?')) {
      return
    }

    try {
      await organizationsApi.removeUser(orgId, userId)
      await fetchOrganization() // Refresh the list
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to remove user')
    }
  }

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="text-center mt-4 text-gray-600 dark:text-gray-400">Loading...</p>
      </div>
    )
  }

  if (!organization) {
    return (
      <div className="p-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Organization Not Found
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Unable to load organization data.
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Organization Members
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage members of {organization.name}
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Invite User Form */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Invite New Member
          </h2>
          <form onSubmit={handleInviteUser} className="flex gap-4">
            <div className="flex-1">
              <input
                type="email"
                placeholder="Enter email address"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                required
              />
            </div>
            <div>
              <select
                value={inviteRole}
                onChange={(e) => setInviteRole(e.target.value)}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="MEMBER">Member</option>
                <option value="STAFF">Staff</option>
                <option value="ADMIN">Admin</option>
              </select>
            </div>
            <button
              type="submit"
              disabled={inviting}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {inviting ? 'Inviting...' : 'Invite'}
            </button>
          </form>
        </div>

        {/* Members List */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Current Members ({organization.users.length})
            </h2>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {organization.users.map((user) => (
              <div key={user.id} className="p-6 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 dark:text-blue-300 font-medium">
                      {(user.firstName?.[0] || user.username[0]).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                      {user.firstName && user.lastName 
                        ? `${user.firstName} ${user.lastName}` 
                        : user.username}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">{user.email}</p>
                    <p className="text-sm text-gray-500 dark:text-gray-500">
                      Joined {new Date(user.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    user.role === 'ADMIN' 
                      ? 'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200'
                      : user.role === 'STAFF'
                      ? 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                  }`}>
                    {user.role}
                  </span>
                  <button
                    onClick={() => handleRemoveUser(user.id)}
                    className="text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300 text-sm font-medium"
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
