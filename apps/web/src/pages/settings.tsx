import { useState } from 'react'
import Link from 'next/link'

export default function Settings() {
  const [user, setUser] = useState({
    firstName: 'John',
    lastName: 'Do<PERSON>',
    email: '<EMAIL>',
    username: 'johndo<PERSON>',
    sobrietyDate: '2024-01-01',
    timezone: 'America/New_York'
  })

  const [notifications, setNotifications] = useState({
    dailyReminders: true,
    meetingAlerts: true,
    journalPrompts: false,
    emergencyAlerts: true,
    emailNotifications: true,
    pushNotifications: true
  })

  const [privacy, setPrivacy] = useState({
    shareProgress: false,
    publicProfile: false,
    dataExport: false
  })

  const [showDeleteAccount, setShowDeleteAccount] = useState(false)

  const handleSaveProfile = () => {
    // TODO: Implement save functionality
    console.log('Saving profile:', user)
  }

  const handleSaveNotifications = () => {
    // TODO: Implement save functionality
    console.log('Saving notifications:', notifications)
  }

  const handleSavePrivacy = () => {
    // TODO: Implement save functionality
    console.log('Saving privacy:', privacy)
  }

  const handleExportData = () => {
    // TODO: Implement data export
    console.log('Exporting user data...')
  }

  const handleDeleteAccount = () => {
    // TODO: Implement account deletion
    console.log('Deleting account...')
    setShowDeleteAccount(false)
  }

  const calculateCleanDays = () => {
    const start = new Date(user.sobrietyDate)
    const now = new Date()
    return Math.floor((now.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              ← Back
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
              <p className="text-gray-600 dark:text-gray-300">Manage your account and preferences</p>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Delete Account Modal */}
        {showDeleteAccount && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-4">
                  Delete Account
                </h2>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Are you sure you want to delete your account? This action cannot be undone and will permanently delete all your data including journal entries, events, and progress tracking.
                </p>
                <div className="flex justify-end space-x-3">
                  <button
                    onClick={() => setShowDeleteAccount(false)}
                    className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDeleteAccount}
                    className="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition-colors"
                  >
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-8">
          {/* Profile Settings */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Profile</h2>
              <p className="text-gray-600 dark:text-gray-300">Update your personal information</p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={user.firstName}
                    onChange={(e) => setUser({...user, firstName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={user.lastName}
                    onChange={(e) => setUser({...user, lastName: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={user.email}
                    onChange={(e) => setUser({...user, email: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Username
                  </label>
                  <input
                    type="text"
                    value={user.username}
                    onChange={(e) => setUser({...user, username: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sobriety Date
                  </label>
                  <input
                    type="date"
                    value={user.sobrietyDate}
                    onChange={(e) => setUser({...user, sobrietyDate: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Current streak: {calculateCleanDays()} days
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Timezone
                  </label>
                  <select
                    value={user.timezone}
                    onChange={(e) => setUser({...user, timezone: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="America/New_York">Eastern Time</option>
                    <option value="America/Chicago">Central Time</option>
                    <option value="America/Denver">Mountain Time</option>
                    <option value="America/Los_Angeles">Pacific Time</option>
                  </select>
                </div>
              </div>
              <div className="mt-6">
                <button
                  onClick={handleSaveProfile}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Save Profile
                </button>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Notifications</h2>
              <p className="text-gray-600 dark:text-gray-300">Choose what notifications you'd like to receive</p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {Object.entries(notifications).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {key === 'dailyReminders' && 'Daily motivation and check-in reminders'}
                        {key === 'meetingAlerts' && 'Notifications for upcoming meetings and appointments'}
                        {key === 'journalPrompts' && 'Weekly prompts to encourage journaling'}
                        {key === 'emergencyAlerts' && 'Critical alerts and emergency notifications'}
                        {key === 'emailNotifications' && 'Receive notifications via email'}
                        {key === 'pushNotifications' && 'Browser and mobile push notifications'}
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => setNotifications({...notifications, [key]: e.target.checked})}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button
                  onClick={handleSaveNotifications}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Save Notifications
                </button>
              </div>
            </div>
          </div>

          {/* Privacy Settings */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Privacy & Data</h2>
              <p className="text-gray-600 dark:text-gray-300">Control your privacy and data settings</p>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white">Share Progress</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Allow sharing your recovery milestones with support network
                    </p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={privacy.shareProgress}
                      onChange={(e) => setPrivacy({...privacy, shareProgress: e.target.checked})}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="border-t pt-6">
                  <h3 className="font-medium text-gray-900 dark:text-white mb-4">Data Management</h3>
                  <div className="space-y-3">
                    <button
                      onClick={handleExportData}
                      className="w-full text-left p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">Export Your Data</h4>
                          <p className="text-sm text-gray-500 dark:text-gray-400">
                            Download a copy of all your data
                          </p>
                        </div>
                        <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                    </button>

                    <button
                      onClick={() => setShowDeleteAccount(true)}
                      className="w-full text-left p-4 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-red-600 dark:text-red-400">Delete Account</h4>
                          <p className="text-sm text-red-500 dark:text-red-400">
                            Permanently delete your account and all data
                          </p>
                        </div>
                        <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </div>
                    </button>
                  </div>
                </div>
              </div>
              <div className="mt-6">
                <button
                  onClick={handleSavePrivacy}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Save Privacy Settings
                </button>
              </div>
            </div>
          </div>

          {/* App Information */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">About</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4 text-sm text-gray-600 dark:text-gray-300">
                <div className="flex justify-between">
                  <span>App Version</span>
                  <span>1.0.0</span>
                </div>
                <div className="flex justify-between">
                  <span>Last Updated</span>
                  <span>December 2024</span>
                </div>
                <div className="flex justify-between">
                  <span>Privacy Policy</span>
                  <Link href="/privacy" className="text-blue-600 hover:text-blue-800">
                    View
                  </Link>
                </div>
                <div className="flex justify-between">
                  <span>Terms of Service</span>
                  <Link href="/terms" className="text-blue-600 hover:text-blue-800">
                    View
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
