'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Star, MapPin, Clock, Users, Search, Filter } from 'lucide-react'
import { meetingsApi } from '@/lib/api'
import { toast } from 'sonner'

interface Meeting {
  id: string
  title: string
  description?: string
  locationName?: string
  address?: string
  latitude?: number
  longitude?: number
  startTime: string
  endTime?: string
  meetingType: string
  avgRating: number
  _count: {
    ratings: number
  }
  distance?: number
}

export default function MeetingsPage() {
  const [meetings, setMeetings] = useState<Meeting[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [userLocation, setUserLocation] = useState<{ lat: number; lng: number } | null>(null)
  const [radius, setRadius] = useState(25)

  useEffect(() => {
    getUserLocationAndFetchMeetings()
  }, [])

  const getUserLocationAndFetchMeetings = async () => {
    try {
      setLoading(true)
      
      // Try to get user's current location
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          async (position) => {
            const { latitude, longitude } = position.coords
            setUserLocation({ lat: latitude, lng: longitude })
            
            // Fetch nearby meetings
            const nearbyMeetings = await meetingsApi.findNearby(latitude, longitude, radius)
            setMeetings(nearbyMeetings)
          },
          async (error) => {
            console.warn('Location access denied, fetching all meetings:', error)
            // Fallback to all meetings if location is denied
            const allMeetings = await meetingsApi.getAll()
            setMeetings(allMeetings.meetings || [])
          }
        )
      } else {
        // Fallback for browsers without geolocation
        const allMeetings = await meetingsApi.getAll()
        setMeetings(allMeetings.meetings || [])
      }
    } catch (error) {
      console.error('Failed to fetch meetings:', error)
      toast.error('Failed to load meetings')
    } finally {
      setLoading(false)
    }
  }

  const handleRateMeeting = async (meetingId: string, rating: number) => {
    try {
      await meetingsApi.rateMeeting(meetingId, rating)
      toast.success('Rating submitted successfully')
      // Refresh meetings to show updated rating
      getUserLocationAndFetchMeetings()
    } catch (error) {
      console.error('Failed to rate meeting:', error)
      toast.error('Failed to submit rating')
    }
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const filteredMeetings = meetings.filter(meeting =>
    meeting.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    meeting.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    meeting.locationName?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const StarRating = ({ rating, onRate, readonly = false }: { rating: number; onRate?: (rating: number) => void; readonly?: boolean }) => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
            } ${!readonly && onRate ? 'cursor-pointer hover:text-yellow-400' : ''}`}
            onClick={() => !readonly && onRate && onRate(star)}
          />
        ))}
        <span className="text-sm text-gray-600 ml-2">
          {rating > 0 ? `${rating.toFixed(1)}` : 'No ratings'}
        </span>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading meetings...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Find Meetings</h1>
            <p className="text-gray-600 mt-1">
              Discover recovery meetings near you
              {userLocation && ` within ${radius} miles`}
            </p>
          </div>
          <Button onClick={getUserLocationAndFetchMeetings} disabled={loading}>
            <Search className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Search meetings by name, description, or location..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <Input
              type="number"
              placeholder="Radius (miles)"
              value={radius}
              onChange={(e) => setRadius(Number(e.target.value))}
              className="w-32"
              min="1"
              max="100"
            />
          </div>
        </div>

        {/* Meetings Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredMeetings.map((meeting) => (
            <Card key={meeting.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{meeting.title}</CardTitle>
                    <CardDescription className="mt-1">
                      <Badge variant="secondary" className="mr-2">
                        {meeting.meetingType}
                      </Badge>
                      {meeting.distance && (
                        <span className="text-sm text-gray-500">
                          {meeting.distance.toFixed(1)} miles away
                        </span>
                      )}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {meeting.description && (
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {meeting.description}
                  </p>
                )}

                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2" />
                    <div>
                      <div>{formatDate(meeting.startTime)}</div>
                      <div>{formatTime(meeting.startTime)}</div>
                    </div>
                  </div>

                  {meeting.locationName && (
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      <div>
                        <div>{meeting.locationName}</div>
                        {meeting.address && (
                          <div className="text-xs text-gray-500">{meeting.address}</div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    <span>{meeting._count.ratings} ratings</span>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between">
                    <StarRating
                      rating={meeting.avgRating}
                      readonly
                    />
                  </div>
                  <div className="mt-2">
                    <p className="text-xs text-gray-500 mb-1">Rate this meeting:</p>
                    <StarRating
                      rating={0}
                      onRate={(rating) => handleRateMeeting(meeting.id, rating)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredMeetings.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <MapPin className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No meetings found</h3>
              <p>Try adjusting your search criteria or location radius.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
