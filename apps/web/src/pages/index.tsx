import { useEffect, useState } from 'react'
import Link from 'next/link'
import { useAuth } from '../contexts/AuthContext'
import { cleanTimeApi, organizationsApi, gratitudeApi, quotesApi } from '../lib/api'

export default function Dashboard() {
  const { user } = useAuth()
  const [cleanTimeData, setCleanTimeData] = useState<any>(null)
  const [organization, setOrganization] = useState<any>(null)
  const [gratitudeEntries, setGratitudeEntries] = useState<any[]>([])
  const [randomQuote, setRandomQuote] = useState<any>(null)
  const [newGratitude, setNewGratitude] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [cleanTime, org, gratitude, quote] = await Promise.all([
        cleanTimeApi.getCurrent().catch(() => null),
        organizationsApi.getMyOrganization().catch(() => null),
        gratitudeApi.getRecent(7).catch(() => []),
        quotesApi.getRandom().catch(() => null),
      ])
      setCleanTimeData(cleanTime)
      setOrganization(org)
      setGratitudeEntries(gratitude)
      setRandomQuote(quote)
    } catch (error) {
      console.error('Failed to fetch data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateDays = () => {
    if (!cleanTimeData?.startDate) return 0
    const startDate = new Date(cleanTimeData.startDate)
    const today = new Date()
    return Math.floor((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
  }

  const totalDays = calculateDays()

  const handlePanicPress = () => {
    window.location.href = '/panic'
  }

  const handleAddGratitude = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newGratitude.trim()) return

    try {
      await gratitudeApi.create({ text: newGratitude.trim() })
      setNewGratitude('')
      // Refresh gratitude entries
      const gratitude = await gratitudeApi.getRecent(7)
      setGratitudeEntries(gratitude)
    } catch (error) {
      console.error('Failed to add gratitude entry:', error)
    }
  }

  const handleDeleteGratitude = async (id: string) => {
    try {
      await gratitudeApi.delete(id)
      // Refresh gratitude entries
      const gratitude = await gratitudeApi.getRecent(7)
      setGratitudeEntries(gratitude)
    } catch (error) {
      console.error('Failed to delete gratitude entry:', error)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Welcome Section */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Welcome back! 👋
        </h1>
        <p className="text-gray-600 dark:text-gray-300">
          Every day is a step forward in your recovery journey
        </p>
      </div>

      {/* Clean Time Counter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border mx-auto max-w-md">
        <div className="p-6">
          <h2 className="text-xl font-semibold text-center mb-4 text-gray-900 dark:text-white">Clean Time</h2>
          <div className="text-center space-y-4">
            <div className="text-4xl font-bold text-green-600">{totalDays}</div>
            <div className="text-lg text-gray-600 dark:text-gray-300">
              {totalDays === 1 ? "Day" : "Days"} Clean
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {totalDays === 0 && "Every journey begins with a single step"}
              {totalDays === 1 && "One day at a time 💪"}
              {totalDays >= 2 && totalDays < 7 && "Building momentum! 🌱"}
              {totalDays >= 7 && totalDays < 30 && "One week strong! 🔥"}
              {totalDays >= 30 && totalDays < 90 && "One month milestone! 🎉"}
              {totalDays >= 90 && totalDays < 365 && "90+ days of strength! 💎"}
              {totalDays >= 365 && "Over a year of recovery! 🏆"}
            </div>
          </div>
        </div>
      </div>

      {/* Random Quote */}
      {randomQuote && (
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-purple-900 dark:text-purple-100">
              ✨ Daily Inspiration
            </h2>
            <Link href="/quotes" className="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 text-sm font-medium">
              View All →
            </Link>
          </div>
          <blockquote className="text-lg text-gray-800 dark:text-gray-200 italic">
            "{randomQuote.text}"
          </blockquote>
          {randomQuote.tags && (
            <div className="flex flex-wrap gap-2 mt-3">
              {randomQuote.tags.split(',').map((tag: string, index: number) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded-full"
                >
                  #{tag.trim()}
                </span>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Gratitude Section */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              🙏 Gratitude List
            </h2>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {gratitudeEntries.length} entries this week
            </span>
          </div>

          {/* Add Gratitude Form */}
          <form onSubmit={handleAddGratitude} className="mb-6">
            <div className="flex gap-3">
              <input
                type="text"
                value={newGratitude}
                onChange={(e) => setNewGratitude(e.target.value)}
                placeholder="What are you grateful for today?"
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
              <button
                type="submit"
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                Add
              </button>
            </div>
          </form>

          {/* Gratitude Entries */}
          <div className="space-y-3">
            {gratitudeEntries.slice(0, 5).map((entry: any) => (
              <div key={entry.id} className="flex items-start justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex-1">
                  <p className="text-gray-800 dark:text-gray-200">{entry.text}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(entry.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <button
                  onClick={() => handleDeleteGratitude(entry.id)}
                  className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 text-sm ml-3"
                >
                  ×
                </button>
              </div>
            ))}

            {gratitudeEntries.length === 0 && (
              <div className="text-center py-6">
                <div className="text-4xl mb-2">🙏</div>
                <p className="text-gray-600 dark:text-gray-300">
                  Start your gratitude practice today
                </p>
              </div>
            )}

            {gratitudeEntries.length > 5 && (
              <div className="text-center pt-3">
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {gratitudeEntries.length - 5} more entries this week
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Dashboard Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              📝
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Journal Entries</h3>
              <p className="text-2xl font-bold text-blue-600">12</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">This month</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              📅
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Upcoming Events</h3>
              <p className="text-2xl font-bold text-green-600">3</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">Next 7 days</p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              🎯
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Goals Progress</h3>
              <p className="text-2xl font-bold text-purple-600">85%</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">This week</p>
            </div>
          </div>
        </div>
      </div>

      {/* Organization Overview - Only show if user has organization */}
      {organization && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Organization Overview</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Houses</h3>
                <Link href="/houses" className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  View All →
                </Link>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Total Houses:</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {organization.houses?.length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Total Capacity:</span>
                  <span className="font-semibold text-gray-900 dark:text-white">
                    {organization.houses?.reduce((sum: number, house: any) => sum + (house.capacity || 0), 0) || 0}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Maintenance</h3>
                <Link href="/maintenance" className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                  View All →
                </Link>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Open Requests:</span>
                  <span className="font-semibold text-orange-600">3</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Completed This Month:</span>
                  <span className="font-semibold text-green-600">12</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Show onboarding prompt if no organization */}
      {!loading && !organization && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Set up your organization
          </h2>
          <p className="text-blue-700 dark:text-blue-300 mb-4">
            Create an organization to manage houses, staff, and maintenance requests.
          </p>
          <Link
            href="/onboarding"
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Get Started
          </Link>
        </div>
      )}

      {/* Quick Emergency Access */}
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">Need Immediate Help?</h3>
            <p className="text-sm text-red-600 dark:text-red-300">Crisis support is available 24/7</p>
          </div>
          <button
            onClick={handlePanicPress}
            className="bg-red-600 text-white hover:bg-red-700 px-6 py-3 rounded-md font-semibold transition-all duration-200 hover:shadow-lg active:scale-95"
          >
            🚨 Emergency
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Recent Activity</h2>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Journal entry added</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Meeting scheduled</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">Yesterday</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">Clean time milestone reached</p>
                <p className="text-xs text-gray-500 dark:text-gray-400">3 days ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
