import { useState, useEffect } from 'react'
import { quotesApi } from '../lib/api'

interface Quote {
  id: string
  text: string
  tags?: string
  createdAt: string
  updatedAt: string
}

export default function Quotes() {
  const [quotes, setQuotes] = useState<Quote[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [tagFilter, setTagFilter] = useState('')
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingQuote, setEditingQuote] = useState<Quote | null>(null)
  const [randomQuote, setRandomQuote] = useState<Quote | null>(null)

  const [newQuote, setNewQuote] = useState({
    text: '',
    tags: ''
  })

  useEffect(() => {
    fetchQuotes()
    fetchRandomQuote()
  }, [searchTerm, tagFilter])

  const fetchQuotes = async () => {
    try {
      const data = await quotesApi.getAll(searchTerm || undefined, tagFilter || undefined)
      setQuotes(data)
    } catch (error) {
      console.error('Failed to fetch quotes:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRandomQuote = async () => {
    try {
      const data = await quotesApi.getRandom()
      setRandomQuote(data)
    } catch (error) {
      console.error('Failed to fetch random quote:', error)
    }
  }

  const handleAddQuote = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newQuote.text.trim()) return

    try {
      await quotesApi.create({
        text: newQuote.text.trim(),
        tags: newQuote.tags.trim() || undefined
      })
      setNewQuote({ text: '', tags: '' })
      setShowAddForm(false)
      fetchQuotes()
      fetchRandomQuote()
    } catch (error) {
      console.error('Failed to create quote:', error)
    }
  }

  const handleEditQuote = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!editingQuote || !editingQuote.text.trim()) return

    try {
      await quotesApi.update(editingQuote.id, {
        text: editingQuote.text.trim(),
        tags: editingQuote.tags?.trim() || undefined
      })
      setEditingQuote(null)
      fetchQuotes()
      fetchRandomQuote()
    } catch (error) {
      console.error('Failed to update quote:', error)
    }
  }

  const handleDeleteQuote = async (id: string) => {
    if (!confirm('Are you sure you want to delete this quote?')) return

    try {
      await quotesApi.delete(id)
      fetchQuotes()
      fetchRandomQuote()
    } catch (error) {
      console.error('Failed to delete quote:', error)
    }
  }

  const parseTagsArray = (tags?: string) => {
    if (!tags) return []
    return tags.split(',').map(tag => tag.trim()).filter(Boolean)
  }

  const formatTagsString = (tagsArray: string[]) => {
    return tagsArray.join(', ')
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Personal Quotes & Affirmations
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            Your collection of inspiring quotes and positive affirmations
          </p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          + Add Quote
        </button>
      </div>

      {/* Random Quote of the Day */}
      {randomQuote && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100">
              ✨ Quote of the Moment
            </h2>
            <button
              onClick={fetchRandomQuote}
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
            >
              🎲 New Quote
            </button>
          </div>
          <blockquote className="text-lg text-gray-800 dark:text-gray-200 italic mb-2">
            "{randomQuote.text}"
          </blockquote>
          {randomQuote.tags && (
            <div className="flex flex-wrap gap-2">
              {parseTagsArray(randomQuote.tags).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                >
                  #{tag}
                </span>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Search and Filter */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Quotes
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by text..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Filter by Tags
            </label>
            <input
              type="text"
              value={tagFilter}
              onChange={(e) => setTagFilter(e.target.value)}
              placeholder="Filter by tags..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>
      </div>

      {/* Add Quote Modal */}
      {showAddForm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Add New Quote</h2>
            </div>
            <form onSubmit={handleAddQuote} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Quote Text *
                </label>
                <textarea
                  value={newQuote.text}
                  onChange={(e) => setNewQuote({ ...newQuote, text: e.target.value })}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your inspiring quote or affirmation..."
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tags (comma separated)
                </label>
                <input
                  type="text"
                  value={newQuote.tags}
                  onChange={(e) => setNewQuote({ ...newQuote, tags: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="motivation, strength, recovery, hope..."
                />
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowAddForm(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Add Quote
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Quote Modal */}
      {editingQuote && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4">
            <div className="p-6 border-b">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Edit Quote</h2>
            </div>
            <form onSubmit={handleEditQuote} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Quote Text *
                </label>
                <textarea
                  value={editingQuote.text}
                  onChange={(e) => setEditingQuote({ ...editingQuote, text: e.target.value })}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tags (comma separated)
                </label>
                <input
                  type="text"
                  value={editingQuote.tags || ''}
                  onChange={(e) => setEditingQuote({ ...editingQuote, tags: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setEditingQuote(null)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Update Quote
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Quotes List */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600 dark:text-gray-400">Loading quotes...</p>
        </div>
      ) : (
        <div className="space-y-4">
          {quotes.map((quote) => (
            <div key={quote.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <blockquote className="text-lg text-gray-800 dark:text-gray-200 mb-3">
                    "{quote.text}"
                  </blockquote>
                  {quote.tags && (
                    <div className="flex flex-wrap gap-2 mb-3">
                      {parseTagsArray(quote.tags).map((tag, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  )}
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Added {new Date(quote.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div className="flex space-x-2 ml-4">
                  <button
                    onClick={() => setEditingQuote(quote)}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteQuote(quote.id)}
                    className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-sm font-medium"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          ))}

          {quotes.length === 0 && (
            <div className="text-center py-12">
              <div className="text-6xl mb-4">💭</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No quotes yet
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Start building your collection of inspiring quotes and affirmations.
              </p>
              <button
                onClick={() => setShowAddForm(true)}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
              >
                Add Your First Quote
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
