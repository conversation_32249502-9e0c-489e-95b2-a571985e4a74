import { useState } from 'react'
import Link from 'next/link'

interface PanicContact {
  id: string
  name: string
  phoneNumber: string
  relationship?: string
  isPrimary: boolean
}

export default function Panic() {
  const [contacts] = useState<PanicContact[]>([
    {
      id: '1',
      name: 'Dr. <PERSON>',
      phoneNumber: '+****************',
      relationship: 'Therapist',
      isPrimary: true
    },
    {
      id: '2',
      name: '<PERSON>',
      phoneNumber: '+****************',
      relationship: 'Sponsor',
      isPrimary: false
    },
    {
      id: '3',
      name: 'Crisis Hotline',
      phoneNumber: '988',
      relationship: 'National Suicide Prevention Lifeline',
      isPrimary: false
    }
  ])

  const [showAddContact, setShowAddContact] = useState(false)
  const [newContact, setNewContact] = useState({
    name: '',
    phoneNumber: '',
    relationship: '',
    isPrimary: false
  })

  const [emergencyActivated, setEmergencyActivated] = useState(false)

  const handleEmergencyPress = () => {
    setEmergencyActivated(true)
    // TODO: Implement actual emergency contact functionality
    console.log('Emergency activated!')
    
    // Reset after 5 seconds for demo
    setTimeout(() => {
      setEmergencyActivated(false)
    }, 5000)
  }

  const handleCallContact = (contact: PanicContact) => {
    // TODO: Implement actual calling functionality
    console.log(`Calling ${contact.name} at ${contact.phoneNumber}`)
    if (typeof window !== 'undefined') {
      window.open(`tel:${contact.phoneNumber}`)
    }
  }

  const handleSaveContact = () => {
    // TODO: Implement save functionality
    console.log('Saving contact:', newContact)
    setShowAddContact(false)
    setNewContact({
      name: '',
      phoneNumber: '',
      relationship: '',
      isPrimary: false
    })
  }

  const primaryContact = contacts.find(c => c.isPrimary)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              ← Back
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Emergency Support</h1>
              <p className="text-gray-600 dark:text-gray-300">Get help when you need it most</p>
            </div>
          </div>
          <button
            onClick={() => setShowAddContact(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            + Add Contact
          </button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {/* Emergency Activated State */}
        {emergencyActivated && (
          <div className="fixed inset-0 z-50 bg-red-600 flex items-center justify-center">
            <div className="text-center text-white">
              <div className="text-6xl mb-4 animate-pulse">🚨</div>
              <h2 className="text-3xl font-bold mb-4">Emergency Activated</h2>
              <p className="text-xl mb-8">Contacting your emergency contacts...</p>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
            </div>
          </div>
        )}

        {/* Add Contact Modal */}
        {showAddContact && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Add Emergency Contact</h2>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Name
                  </label>
                  <input
                    type="text"
                    value={newContact.name}
                    onChange={(e) => setNewContact({...newContact, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Contact name..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={newContact.phoneNumber}
                    onChange={(e) => setNewContact({...newContact, phoneNumber: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="+****************"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Relationship
                  </label>
                  <input
                    type="text"
                    value={newContact.relationship}
                    onChange={(e) => setNewContact({...newContact, relationship: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Therapist, Sponsor, Friend..."
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isPrimary"
                    checked={newContact.isPrimary}
                    onChange={(e) => setNewContact({...newContact, isPrimary: e.target.checked})}
                    className="mr-2"
                  />
                  <label htmlFor="isPrimary" className="text-sm text-gray-700 dark:text-gray-300">
                    Set as primary contact
                  </label>
                </div>
              </div>
              
              <div className="p-6 border-t flex justify-end space-x-3">
                <button
                  onClick={() => setShowAddContact(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveContact}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Save Contact
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="space-y-8">
          {/* Emergency Button */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-200 dark:border-red-800 p-8">
            <div className="text-center space-y-6">
              <div className="text-6xl">🚨</div>
              <h2 className="text-2xl font-bold text-red-600 dark:text-red-400">
                Emergency Support
              </h2>
              <p className="text-gray-600 dark:text-gray-300 max-w-md mx-auto">
                If you're in crisis or having thoughts of using, press the button below to immediately contact your support network.
              </p>
              
              <button
                onClick={handleEmergencyPress}
                className="w-full max-w-xs mx-auto bg-red-600 text-white hover:bg-red-700 shadow-lg px-8 py-6 rounded-lg text-xl font-bold transition-all duration-200 hover:shadow-xl active:scale-95 panic-pulse"
              >
                <div className="flex items-center justify-center space-x-3">
                  <span className="text-2xl">⚠️</span>
                  <span>GET HELP NOW</span>
                </div>
              </button>

              {primaryContact && (
                <div className="mt-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <p className="text-sm text-red-700 dark:text-red-300 mb-2">
                    Primary contact will be called first:
                  </p>
                  <p className="font-semibold text-red-800 dark:text-red-200">
                    {primaryContact.name} - {primaryContact.phoneNumber}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Quick Call Buttons */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Quick Call
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Call your support contacts directly
              </p>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {contacts.map((contact) => (
                  <button
                    key={contact.id}
                    onClick={() => handleCallContact(contact)}
                    className="flex items-center space-x-4 p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                  >
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-xl">📞</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900 dark:text-white">
                          {contact.name}
                        </h4>
                        {contact.isPrimary && (
                          <span className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 text-xs rounded-full">
                            Primary
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {contact.phoneNumber}
                      </p>
                      {contact.relationship && (
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {contact.relationship}
                        </p>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Crisis Resources */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                Crisis Resources
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                National and local crisis support services
              </p>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100">
                      National Suicide Prevention Lifeline
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      24/7 crisis support and suicide prevention
                    </p>
                  </div>
                  <button
                    onClick={() => handleCallContact({ id: 'crisis', name: 'Crisis Hotline', phoneNumber: '988', isPrimary: false })}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Call 988
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-green-900 dark:text-green-100">
                      Crisis Text Line
                    </h4>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      Text HOME to 741741 for crisis support
                    </p>
                  </div>
                  <button
                    onClick={() => window.open('sms:741741?body=HOME')}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    Text
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div>
                    <h4 className="font-semibold text-purple-900 dark:text-purple-100">
                      SAMHSA National Helpline
                    </h4>
                    <p className="text-sm text-purple-700 dark:text-purple-300">
                      Treatment referral and information service
                    </p>
                  </div>
                  <button
                    onClick={() => handleCallContact({ id: 'samhsa', name: 'SAMHSA', phoneNumber: '**************', isPrimary: false })}
                    className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                  >
                    Call
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
