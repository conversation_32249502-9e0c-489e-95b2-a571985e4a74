import { useState } from 'react'
import Link from 'next/link'

interface Event {
  id: string
  title: string
  description?: string
  startTime: Date
  endTime?: Date
  type: 'MEETING' | 'APPOINTMENT' | 'THERAPY' | 'SUPPORT_GROUP' | 'PERSONAL' | 'REMINDER'
  location?: string
}

export default function Calendar() {
  const [events] = useState<Event[]>([
    {
      id: '1',
      title: 'AA Meeting',
      description: 'Weekly Alcoholics Anonymous meeting',
      startTime: new Date('2024-12-20T19:00:00'),
      endTime: new Date('2024-12-20T20:30:00'),
      type: 'SUPPORT_GROUP',
      location: 'Community Center'
    },
    {
      id: '2',
      title: 'Therapy Session',
      description: 'Individual therapy with Dr<PERSON> <PERSON>',
      startTime: new Date('2024-12-22T14:00:00'),
      endTime: new Date('2024-12-22T15:00:00'),
      type: 'THERAPY',
      location: 'Wellness Clinic'
    },
    {
      id: '3',
      title: 'Meditation Practice',
      description: 'Daily mindfulness meditation',
      startTime: new Date('2024-12-21T07:00:00'),
      endTime: new Date('2024-12-21T07:30:00'),
      type: 'PERSONAL'
    }
  ])

  const [showNewEvent, setShowNewEvent] = useState(false)
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    startTime: '',
    endTime: '',
    type: 'PERSONAL' as Event['type'],
    location: ''
  })

  const [currentDate] = useState(new Date())

  const getTypeColor = (type: Event['type']) => {
    const colors = {
      MEETING: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      APPOINTMENT: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      THERAPY: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      SUPPORT_GROUP: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
      PERSONAL: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
      REMINDER: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    }
    return colors[type]
  }

  const getTypeIcon = (type: Event['type']) => {
    const icons = {
      MEETING: '💼',
      APPOINTMENT: '🏥',
      THERAPY: '🧠',
      SUPPORT_GROUP: '👥',
      PERSONAL: '🧘',
      REMINDER: '⏰'
    }
    return icons[type]
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const upcomingEvents = events
    .filter(event => event.startTime >= currentDate)
    .sort((a, b) => a.startTime.getTime() - b.startTime.getTime())
    .slice(0, 5)

  const todayEvents = events.filter(event => {
    const today = new Date()
    return event.startTime.toDateString() === today.toDateString()
  })

  const handleSaveEvent = () => {
    // TODO: Implement save functionality
    console.log('Saving event:', newEvent)
    setShowNewEvent(false)
    setNewEvent({
      title: '',
      description: '',
      startTime: '',
      endTime: '',
      type: 'PERSONAL',
      location: ''
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/" className="text-blue-600 hover:text-blue-800">
              ← Back
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Calendar</h1>
              <p className="text-gray-600 dark:text-gray-300">Manage your appointments and events</p>
            </div>
          </div>
          <button
            onClick={() => setShowNewEvent(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            + Add Event
          </button>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8">
        {/* New Event Modal */}
        {showNewEvent && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Add New Event</h2>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Event Title
                  </label>
                  <input
                    type="text"
                    value={newEvent.title}
                    onChange={(e) => setNewEvent({...newEvent, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Enter event title..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Type
                  </label>
                  <select
                    value={newEvent.type}
                    onChange={(e) => setNewEvent({...newEvent, type: e.target.value as Event['type']})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  >
                    <option value="PERSONAL">Personal</option>
                    <option value="THERAPY">Therapy</option>
                    <option value="SUPPORT_GROUP">Support Group</option>
                    <option value="MEETING">Meeting</option>
                    <option value="APPOINTMENT">Appointment</option>
                    <option value="REMINDER">Reminder</option>
                  </select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Start Time
                    </label>
                    <input
                      type="datetime-local"
                      value={newEvent.startTime}
                      onChange={(e) => setNewEvent({...newEvent, startTime: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      End Time (Optional)
                    </label>
                    <input
                      type="datetime-local"
                      value={newEvent.endTime}
                      onChange={(e) => setNewEvent({...newEvent, endTime: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Location (Optional)
                  </label>
                  <input
                    type="text"
                    value={newEvent.location}
                    onChange={(e) => setNewEvent({...newEvent, location: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Enter location..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Description (Optional)
                  </label>
                  <textarea
                    value={newEvent.description}
                    onChange={(e) => setNewEvent({...newEvent, description: e.target.value})}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Add any additional details..."
                  />
                </div>
              </div>
              
              <div className="p-6 border-t flex justify-end space-x-3">
                <button
                  onClick={() => setShowNewEvent(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEvent}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Save Event
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Today's Events */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Today's Events
                </h2>
                <p className="text-gray-600 dark:text-gray-300">
                  {formatDate(new Date())}
                </p>
              </div>
              <div className="p-6">
                {todayEvents.length > 0 ? (
                  <div className="space-y-4">
                    {todayEvents.map((event) => (
                      <div key={event.id} className="flex items-start space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="text-2xl">{getTypeIcon(event.type)}</div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold text-gray-900 dark:text-white">{event.title}</h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(event.type)}`}>
                              {event.type.replace('_', ' ')}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                            {formatTime(event.startTime)}
                            {event.endTime && ` - ${formatTime(event.endTime)}`}
                          </p>
                          {event.location && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">📍 {event.location}</p>
                          )}
                          {event.description && (
                            <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">{event.description}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">📅</div>
                    <p className="text-gray-600 dark:text-gray-300">No events scheduled for today</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Upcoming Events */}
          <div>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Upcoming Events
                </h2>
              </div>
              <div className="p-6">
                {upcomingEvents.length > 0 ? (
                  <div className="space-y-4">
                    {upcomingEvents.map((event) => (
                      <div key={event.id} className="border-l-4 border-blue-500 pl-4">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="text-lg">{getTypeIcon(event.type)}</span>
                          <h3 className="font-medium text-gray-900 dark:text-white text-sm">{event.title}</h3>
                        </div>
                        <p className="text-xs text-gray-500 dark:text-gray-400">
                          {event.startTime.toLocaleDateString()} at {formatTime(event.startTime)}
                        </p>
                        {event.location && (
                          <p className="text-xs text-gray-500 dark:text-gray-400">📍 {event.location}</p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">🗓️</div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">No upcoming events</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
