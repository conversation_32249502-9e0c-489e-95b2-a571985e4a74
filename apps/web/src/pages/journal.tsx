import { useState, useEffect } from 'react'
import { journalApi } from '../lib/api'

interface JournalEntry {
  id: string
  title: string
  content?: string
  mood?: number
  tags: string[]
  createdAt: string
  updatedAt: string
}

export default function Journal() {
  const [entries, setEntries] = useState<JournalEntry[]>([])
  const [loading, setLoading] = useState(true)

  const [showNewEntry, setShowNewEntry] = useState(false)
  const [newEntry, setNewEntry] = useState({
    title: '',
    content: '',
    mood: 5,
    tags: ''
  })

  useEffect(() => {
    fetchEntries()
  }, [])

  const fetchEntries = async () => {
    try {
      const data = await journalApi.getAll()
      setEntries(data)
    } catch (error) {
      console.error('Failed to fetch journal entries:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSaveEntry = async () => {
    try {
      const tags = newEntry.tags.split(',').map(tag => tag.trim()).filter(Boolean)
      await journalApi.create({
        title: newEntry.title,
        content: newEntry.content,
        mood: newEntry.mood,
        tags,
      })
      setShowNewEntry(false)
      setNewEntry({ title: '', content: '', mood: 5, tags: '' })
      fetchEntries()
    } catch (error) {
      console.error('Failed to create journal entry:', error)
    }
  }

  const getMoodEmoji = (mood: number) => {
    if (mood >= 8) return '😊'
    if (mood >= 6) return '🙂'
    if (mood >= 4) return '😐'
    if (mood >= 2) return '😔'
    return '😢'
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Journal</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">Your private thoughts and reflections</p>
          </div>
          <button
            onClick={() => setShowNewEntry(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            + New Entry
          </button>
        </div>
      </div>
        {/* New Entry Modal */}
        {showNewEntry && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-2xl mx-4">
              <div className="p-6 border-b">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">New Journal Entry</h2>
              </div>
              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={newEntry.title}
                    onChange={(e) => setNewEntry({...newEntry, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Give your entry a title..."
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    How are you feeling? (1-10)
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={newEntry.mood}
                    onChange={(e) => setNewEntry({...newEntry, mood: parseInt(e.target.value)})}
                    className="w-full"
                  />
                  <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>😢 1</span>
                    <span className="text-lg">{getMoodEmoji(newEntry.mood)} {newEntry.mood}</span>
                    <span>😊 10</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Your thoughts
                  </label>
                  <textarea
                    value={newEntry.content}
                    onChange={(e) => setNewEntry({...newEntry, content: e.target.value})}
                    rows={8}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="Write about your day, feelings, challenges, victories..."
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags (comma separated)
                  </label>
                  <input
                    type="text"
                    value={newEntry.tags}
                    onChange={(e) => setNewEntry({...newEntry, tags: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="hope, progress, challenge, victory..."
                  />
                </div>
              </div>
              
              <div className="p-6 border-t flex justify-end space-x-3">
                <button
                  onClick={() => setShowNewEntry(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSaveEntry}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Save Entry
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Journal Entries */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600 dark:text-gray-400">Loading entries...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {entries.map((entry) => (
            <div key={entry.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {entry.title}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                    <span>{new Date(entry.createdAt).toLocaleDateString()}</span>
                    {entry.mood && (
                      <span className="flex items-center space-x-1">
                        <span>Mood:</span>
                        <span>{getMoodEmoji(entry.mood)} {entry.mood}/10</span>
                      </span>
                    )}
                  </div>
                </div>
                <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                  </svg>
                </button>
              </div>
              
              {entry.content && (
                <p className="text-gray-700 dark:text-gray-300 mb-4 leading-relaxed">
                  {entry.content}
                </p>
              )}
              
              {entry.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {entry.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          ))}
          </div>
        )}

        {!loading && entries.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Start Your Journal
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Writing about your thoughts and feelings can be a powerful tool in recovery.
            </p>
            <button
              onClick={() => setShowNewEntry(true)}
              className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
            >
              Write Your First Entry
            </button>
          </div>
        )}
    </div>
  )
}
