import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'
import { useAuth } from '../contexts/AuthContext'

interface SidebarProps {
  isCollapsed: boolean
  onToggle: () => void
}

interface NavItem {
  name: string
  href: string
  icon: string
  adminOnly?: boolean
}

const navItems: NavItem[] = [
  { name: 'Dashboard', href: '/', icon: '🏠' },
  { name: 'Journal', href: '/journal', icon: '📝' },
  { name: 'Quotes', href: '/quotes', icon: '💭' },
  { name: 'Calendar', href: '/calendar', icon: '📅' },
  { name: 'Emergency', href: '/emergency', icon: '🚨' },
  { name: 'Members', href: '/org/members', icon: '👥', adminOnly: true },
  { name: 'Org Settings', href: '/org/settings', icon: '🏢', adminOnly: true },
  { name: 'Settings', href: '/settings', icon: '⚙️' },
]

export default function Sidebar({ isCollapsed, onToggle }: SidebarProps) {
  const { user, logout } = useAuth()
  const router = useRouter()

  const isOrgAdmin = user?.role === 'ADMIN'
  const hasOrganization = !!user?.organizationId

  const filteredNavItems = navItems.filter(item => {
    if (item.adminOnly && !isOrgAdmin) return false
    return true
  })

  return (
    <div className={`bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 ${
      isCollapsed ? 'w-16' : 'w-64'
    } flex flex-col h-screen`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                Recovery Connect
              </h1>
              {user && (
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  {user.firstName || user.username}
                </p>
              )}
            </div>
          )}
          <button
            onClick={onToggle}
            className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            {isCollapsed ? '→' : '←'}
          </button>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {filteredNavItems.map((item) => {
          const isActive = router.pathname === item.href || 
            (item.href !== '/' && router.pathname.startsWith(item.href))
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive
                  ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
              title={isCollapsed ? item.name : undefined}
            >
              <span className="text-lg">{item.icon}</span>
              {!isCollapsed && (
                <span className="ml-3">{item.name}</span>
              )}
            </Link>
          )
        })}
      </nav>

      {/* Organization Status */}
      {!isCollapsed && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          {hasOrganization ? (
            <div className="text-sm text-gray-600 dark:text-gray-300">
              <p className="font-medium">Organization Member</p>
              <p className="text-xs">Role: {user?.role}</p>
            </div>
          ) : (
            <div className="text-sm">
              <p className="text-gray-600 dark:text-gray-300 mb-2">
                No organization
              </p>
              <Link
                href="/onboarding"
                className="text-blue-600 dark:text-blue-400 hover:underline text-xs"
              >
                Join or create one →
              </Link>
            </div>
          )}
        </div>
      )}

      {/* User Actions */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700">
        {isCollapsed ? (
          <button
            onClick={logout}
            className="w-full p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="Sign out"
          >
            🚪
          </button>
        ) : (
          <button
            onClick={logout}
            className="w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors text-left"
          >
            🚪 Sign out
          </button>
        )}
      </div>
    </div>
  )
}
