import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAuth } from '../contexts/AuthContext'
import Sidebar from './Sidebar'
import ProtectedRoute from './ProtectedRoute'

interface LayoutProps {
  children: React.ReactNode
}

// Pages that should NOT have the sidebar
const NO_SIDEBAR_PAGES = [
  '/login',
  '/register',
]

// Pages that should have full-screen layout (no sidebar, but still authenticated)
const FULL_SCREEN_PAGES = [
  '/onboarding', // Organization onboarding might need full screen
]

export default function Layout({ children }: LayoutProps) {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  // Load sidebar state from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('sidebar-collapsed')
    if (saved !== null) {
      setSidebarCollapsed(JSON.parse(saved))
    }
  }, [])

  // Save sidebar state to localStorage
  const toggleSidebar = () => {
    const newState = !sidebarCollapsed
    setSidebarCollapsed(newState)
    localStorage.setItem('sidebar-collapsed', JSON.stringify(newState))
  }

  // Check if current page should not have sidebar
  const shouldShowSidebar = isAuthenticated &&
    !NO_SIDEBAR_PAGES.includes(router.pathname) &&
    !FULL_SCREEN_PAGES.includes(router.pathname)

  // For non-authenticated pages or pages that explicitly don't need sidebar
  if (!isAuthenticated || NO_SIDEBAR_PAGES.includes(router.pathname)) {
    return <>{children}</>
  }

  // For authenticated pages that need full screen (like onboarding)
  if (FULL_SCREEN_PAGES.includes(router.pathname)) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          {children}
        </div>
      </ProtectedRoute>
    )
  }

  // For authenticated pages with sidebar
  return (
    <ProtectedRoute>
      <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
        <Sidebar isCollapsed={sidebarCollapsed} onToggle={toggleSidebar} />
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </ProtectedRoute>
  )
}
