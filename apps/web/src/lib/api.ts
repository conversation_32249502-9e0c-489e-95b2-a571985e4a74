import axios from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  register: async (data: {
    email: string
    username: string
    password: string
    firstName?: string
    lastName?: string
  }) => {
    const response = await api.post('/auth/register', data)
    return response.data
  },

  login: async (data: { email: string; password: string }) => {
    const response = await api.post('/auth/login', data)
    if (response.data.access_token) {
      localStorage.setItem('auth_token', response.data.access_token)
    }
    return response.data
  },

  logout: async () => {
    await api.post('/auth/logout')
    localStorage.removeItem('auth_token')
  },

  getProfile: async () => {
    const response = await api.get('/auth/profile')
    return response.data
  },
}

// Journal API
export const journalApi = {
  getAll: async () => {
    const response = await api.get('/journal')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/journal/${id}`)
    return response.data
  },

  create: async (data: {
    title: string
    content: string
    mood?: number
    tags?: string[]
  }) => {
    const response = await api.post('/journal', data)
    return response.data
  },

  update: async (id: string, data: {
    title?: string
    content?: string
    mood?: number
    tags?: string[]
  }) => {
    const response = await api.patch(`/journal/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/journal/${id}`)
  },
}

// Clean Time API
export const cleanTimeApi = {
  getCurrent: async () => {
    const response = await api.get('/clean-time/current')
    return response.data
  },

  getStats: async () => {
    const response = await api.get('/clean-time/stats')
    return response.data
  },

  create: async (data: { startDate: string; notes?: string }) => {
    const response = await api.post('/clean-time', data)
    return response.data
  },

  update: async (id: string, data: { endDate?: string; notes?: string }) => {
    const response = await api.patch(`/clean-time/${id}`, data)
    return response.data
  },
}

// Events API
export const eventsApi = {
  getAll: async () => {
    const response = await api.get('/events')
    return response.data
  },

  getUpcoming: async () => {
    const response = await api.get('/events/upcoming')
    return response.data
  },

  create: async (data: {
    title: string
    description?: string
    startTime: string
    endTime?: string
    location?: string
    type?: string
  }) => {
    const response = await api.post('/events', data)
    return response.data
  },

  update: async (id: string, data: any) => {
    const response = await api.patch(`/events/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/events/${id}`)
  },
}

// Panic Contacts API
export const panicApi = {
  getContacts: async () => {
    const response = await api.get('/panic/contacts')
    return response.data
  },

  getPrimary: async () => {
    const response = await api.get('/panic/contacts/primary')
    return response.data
  },

  createContact: async (data: {
    name: string
    phoneNumber: string
    relationship?: string
    isPrimary?: boolean
  }) => {
    const response = await api.post('/panic/contacts', data)
    return response.data
  },

  updateContact: async (id: string, data: any) => {
    const response = await api.patch(`/panic/contacts/${id}`, data)
    return response.data
  },

  deleteContact: async (id: string) => {
    await api.delete(`/panic/contacts/${id}`)
  },
}

// Organizations API
export const organizationsApi = {
  create: async (data: {
    name: string
    description?: string
    address?: string
    phone?: string
    email?: string
    website?: string
  }) => {
    const response = await api.post('/organizations', data)
    // Update token if provided
    if (response.data.access_token) {
      localStorage.setItem('auth_token', response.data.access_token)
    }
    return response.data
  },

  getMyOrganization: async () => {
    const response = await api.get('/organizations/my-organization')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/organizations/${id}`)
    return response.data
  },

  update: async (id: string, data: any) => {
    const response = await api.patch(`/organizations/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/organizations/${id}`)
  },

  inviteUser: async (organizationId: string, email: string, role: string) => {
    const response = await api.post(`/organizations/${organizationId}/invite`, {
      email,
      role,
    })
    return response.data
  },

  removeUser: async (organizationId: string, userId: string) => {
    const response = await api.delete(`/organizations/${organizationId}/remove/${userId}`)
    return response.data
  },
}

// Quotes API
export const quotesApi = {
  getAll: async (search?: string, tags?: string) => {
    const params = new URLSearchParams()
    if (search) params.append('search', search)
    if (tags) params.append('tags', tags)
    const response = await api.get(`/quotes?${params.toString()}`)
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/quotes/${id}`)
    return response.data
  },

  getRandom: async () => {
    const response = await api.get('/quotes/random')
    return response.data
  },

  create: async (data: { text: string; tags?: string }) => {
    const response = await api.post('/quotes', data)
    return response.data
  },

  update: async (id: string, data: { text?: string; tags?: string }) => {
    const response = await api.patch(`/quotes/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/quotes/${id}`)
  },
}

// Gratitude API
export const gratitudeApi = {
  getAll: async (limit?: number) => {
    const params = new URLSearchParams()
    if (limit) params.append('limit', limit.toString())
    const response = await api.get(`/gratitude?${params.toString()}`)
    return response.data
  },

  getRecent: async (days?: number) => {
    const params = new URLSearchParams()
    if (days) params.append('days', days.toString())
    const response = await api.get(`/gratitude/recent?${params.toString()}`)
    return response.data
  },

  getStats: async () => {
    const response = await api.get('/gratitude/stats')
    return response.data
  },

  create: async (data: { text: string }) => {
    const response = await api.post('/gratitude', data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/gratitude/${id}`)
  },
}

// Houses API
export const housesApi = {
  getAll: async () => {
    const response = await api.get('/houses')
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/houses/${id}`)
    return response.data
  },

  create: async (data: {
    name: string
    address: string
    capacity?: number
    notes?: string
  }) => {
    const response = await api.post('/houses', data)
    return response.data
  },

  update: async (id: string, data: any) => {
    const response = await api.patch(`/houses/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/houses/${id}`)
  },
}

// Maintenance API
export const maintenanceApi = {
  getAll: async (houseId?: string) => {
    const url = houseId ? `/maintenance?houseId=${houseId}` : '/maintenance'
    const response = await api.get(url)
    return response.data
  },

  getById: async (id: string) => {
    const response = await api.get(`/maintenance/${id}`)
    return response.data
  },

  create: async (data: {
    title: string
    description?: string
    priority?: 'low' | 'medium' | 'high'
    houseId: string
    notes?: string
  }) => {
    const response = await api.post('/maintenance', data)
    return response.data
  },

  update: async (id: string, data: {
    title?: string
    description?: string
    priority?: 'low' | 'medium' | 'high'
    status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
    notes?: string
  }) => {
    const response = await api.patch(`/maintenance/${id}`, data)
    return response.data
  },

  delete: async (id: string) => {
    await api.delete(`/maintenance/${id}`)
  },
}

export default api
