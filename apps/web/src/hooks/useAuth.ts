import { useAuth as useAuthContext } from '../contexts/AuthContext'

export function useIsOrgAdmin() {
  const { user } = useAuthContext()
  return user?.role === 'ADMIN'
}

export function useOrgId() {
  const { user } = useAuthContext()
  return user?.organizationId || null
}

export function useHasOrganization() {
  const { user } = useAuthContext()
  return !!user?.organizationId
}

export function useIsOrgMember() {
  const { user } = useAuthContext()
  const allowedRoles = ['MEMBER', 'STAFF', 'ADMIN']
  return user?.organizationId && allowedRoles.includes(user.role)
}

export function useUserRole() {
  const { user } = useAuthContext()
  return user?.role || 'INDIVIDUAL'
}
