const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

async function testCompleteFlow() {
  console.log('🧪 Testing Recovery Connect Multi-Tenant Flow\n');
  
  let authToken = '';
  let organizationId = '';
  let houseId = '';
  let maintenanceId = '';

  try {
    // Step 1: Register a new user
    const timestamp = Date.now().toString().slice(-6); // Last 6 digits
    const testEmail = `admin${timestamp}@testorg.com`;
    const testUsername = `admin${timestamp}`;

    console.log('1️⃣ Registering new user...');
    const registerResponse = await axios.post(`${API_BASE}/auth/register`, {
      email: testEmail,
      username: testUsername,
      password: 'password123',
      firstName: 'Test',
      lastName: 'Admin'
    });
    console.log('✅ User registered successfully');

    // Step 2: Login
    console.log('\n2️⃣ Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: testEmail,
      password: 'password123'
    });
    authToken = loginResponse.data.access_token;
    console.log('✅ Login successful, token received');

    // Step 3: Check if user has organization (should be null initially)
    console.log('\n3️⃣ Checking user organization status...');
    const orgCheckResponse = await axios.get(`${API_BASE}/organizations/my-organization`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Organization status:', orgCheckResponse.data ? 'Has organization' : 'No organization');

    // Step 4: Create organization
    console.log('\n4️⃣ Creating organization...');
    const orgResponse = await axios.post(`${API_BASE}/organizations`, {
      name: 'Test Recovery Center',
      description: 'A test organization for recovery services',
      address: '123 Recovery St, Hope City, HC 12345',
      phone: '(*************',
      email: '<EMAIL>'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    organizationId = orgResponse.data.organization.id;
    authToken = orgResponse.data.access_token; // Update token with new role
    console.log('✅ Organization created:', orgResponse.data.organization.name);

    // Step 5: Verify user is now admin of organization
    console.log('\n5️⃣ Verifying user role...');
    const profileResponse = await axios.get(`${API_BASE}/auth/profile`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ User role:', profileResponse.data.role);
    console.log('✅ Organization ID:', profileResponse.data.organizationId);

    // Step 6: Create houses
    console.log('\n6️⃣ Creating houses...');
    const house1Response = await axios.post(`${API_BASE}/houses`, {
      name: 'Main House',
      address: '456 Recovery Ave, Hope City, HC 12345',
      capacity: 12,
      notes: 'Primary residential facility'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    houseId = house1Response.data.id;
    console.log('✅ House 1 created:', house1Response.data.name);

    const house2Response = await axios.post(`${API_BASE}/houses`, {
      name: 'Transitional House',
      address: '789 Hope Blvd, Hope City, HC 12345',
      capacity: 8,
      notes: 'For residents transitioning to independent living'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ House 2 created:', house2Response.data.name);

    // Step 7: List all houses
    console.log('\n7️⃣ Listing all houses...');
    const housesResponse = await axios.get(`${API_BASE}/houses`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Total houses:', housesResponse.data.length);

    // Step 8: Create maintenance requests
    console.log('\n8️⃣ Creating maintenance requests...');
    const maintenance1Response = await axios.post(`${API_BASE}/maintenance`, {
      title: 'Fix leaky faucet in kitchen',
      description: 'The kitchen faucet has been dripping for several days',
      priority: 'medium',
      houseId: houseId,
      notes: 'Located in main kitchen area'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    maintenanceId = maintenance1Response.data.id;
    console.log('✅ Maintenance request 1 created:', maintenance1Response.data.title);

    const maintenance2Response = await axios.post(`${API_BASE}/maintenance`, {
      title: 'Replace broken window',
      description: 'Window in bedroom 3 is cracked and needs replacement',
      priority: 'high',
      houseId: houseId,
      notes: 'Safety concern - needs immediate attention'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Maintenance request 2 created:', maintenance2Response.data.title);

    // Step 9: List maintenance requests
    console.log('\n9️⃣ Listing maintenance requests...');
    const maintenanceResponse = await axios.get(`${API_BASE}/maintenance`, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Total maintenance requests:', maintenanceResponse.data.length);

    // Step 10: Update maintenance status
    console.log('\n🔟 Updating maintenance status...');
    const updateResponse = await axios.patch(`${API_BASE}/maintenance/${maintenanceId}`, {
      status: 'in_progress',
      notes: 'Plumber has been contacted and will arrive tomorrow'
    }, {
      headers: { Authorization: `Bearer ${authToken}` }
    });
    console.log('✅ Maintenance status updated to:', updateResponse.data.status);

    // Step 11: Test data isolation (create second user)
    console.log('\n1️⃣1️⃣ Testing data isolation...');
    const user2Email = `user2${timestamp}@different.com`;
    const user2Username = `user2${timestamp}`;
    const user2Response = await axios.post(`${API_BASE}/auth/register`, {
      email: user2Email,
      username: user2Username,
      password: 'password123',
      firstName: 'User',
      lastName: 'Two'
    });
    
    const login2Response = await axios.post(`${API_BASE}/auth/login`, {
      email: user2Email,
      password: 'password123'
    });
    const user2Token = login2Response.data.access_token;

    // Try to access houses with user2 (should fail or return empty)
    try {
      const user2HousesResponse = await axios.get(`${API_BASE}/houses`, {
        headers: { Authorization: `Bearer ${user2Token}` }
      });
      console.log('❌ Data isolation test failed - user2 can see houses');
    } catch (error) {
      console.log('✅ Data isolation working - user2 cannot access houses without organization');
    }

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📊 Test Summary:');
    console.log(`- Organization ID: ${organizationId}`);
    console.log(`- Houses created: 2`);
    console.log(`- Maintenance requests: 2`);
    console.log(`- Data isolation: ✅ Working`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testCompleteFlow();
