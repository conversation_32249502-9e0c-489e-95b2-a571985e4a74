# Recovery Connect Multi-Tenant Manual Testing Script

## Prerequisites
- Backend server running on http://localhost:3001
- Frontend web app running on http://localhost:3000
- Database seeded with demo data

## Test Scenarios

### 🟩 1. Individual User Flow
**Objective**: Test standalone recovery user experience

1. **Register as Individual User**
   - Go to http://localhost:3000
   - Click "Sign up" or register
   - Email: `<EMAIL>`
   - Username: `individual_user`
   - Password: `test123`
   - First Name: `Individual`
   - Last Name: `User`

2. **Verify Individual Dashboard**
   - ✅ Should see collapsible sidebar
   - ✅ Should NOT see "Members" or "Org Settings" in sidebar
   - ✅ Should see "No organization" status in sidebar
   - ✅ Should see "Join or create one →" link
   - ✅ Should see personal features: Journal, Calendar, Emergency, Settings

3. **Test Personal Features**
   - ✅ Journal should work (create/view entries)
   - ✅ Calendar should work
   - ✅ Emergency contacts should work
   - ✅ Settings should work

### 🟩 2. Organization Creation Flow
**Objective**: Test creating an organization and becoming admin

1. **Create Organization**
   - Click "Join or create one →" or go to `/onboarding`
   - Fill out organization form:
     - Name: `Test Recovery Center`
     - Description: `A test recovery center`
     - Address: `123 Test St, Test City, TC 12345`
     - Phone: `(*************`
     - Email: `<EMAIL>`
   - Submit form

2. **Verify Admin Status**
   - ✅ Should automatically redirect to dashboard
   - ✅ Sidebar should now show "Members" and "Org Settings"
   - ✅ Should see organization name in sidebar
   - ✅ Role should show as "ADMIN"
   - ✅ Should see organization management cards on dashboard

### 🟩 3. Organization Admin Features
**Objective**: Test admin-only functionality

1. **Test Organization Settings**
   - Click "Org Settings" in sidebar
   - ✅ Should load organization settings page
   - ✅ Form should be pre-filled with current data
   - ✅ Should be able to update organization info
   - ✅ Should show success message after update

2. **Test Member Management**
   - Click "Members" in sidebar
   - ✅ Should load members page
   - ✅ Should show current members list
   - ✅ Should show invite form

3. **Test User Invitation**
   - In invite form, enter:
     - Email: `<EMAIL>`
     - Role: `MEMBER`
   - Click "Invite"
   - ✅ Should show success message
   - ✅ Member should appear in members list

4. **Test House Management**
   - Go to houses page
   - ✅ Should be able to create houses
   - ✅ Should be able to view houses
   - ✅ Should be able to edit houses

### 🟩 4. Organization Member Flow
**Objective**: Test member user experience

1. **Register Member User**
   - Open new incognito window
   - Register with email: `<EMAIL>`
   - Username: `member_user`
   - Password: `test123`

2. **Test Member Access**
   - ✅ Should see organization name in sidebar
   - ✅ Should NOT see "Members" or "Org Settings" in sidebar
   - ✅ Role should show as "MEMBER"
   - ✅ Should be able to view houses
   - ✅ Should NOT be able to create/edit houses

### 🟩 5. Data Isolation Testing
**Objective**: Verify multi-tenant data isolation

1. **Create Second Organization**
   - Register new user: `<EMAIL>`
   - Create organization: `Second Recovery Center`

2. **Verify Isolation**
   - ✅ Admin2 should not see houses from first organization
   - ✅ Admin2 should not see members from first organization
   - ✅ Admin2 should only see their own organization data

### 🟩 6. User Removal Testing
**Objective**: Test removing users from organization

1. **Remove Member**
   - As admin, go to Members page
   - Click "Remove" next to member
   - Confirm removal

2. **Verify Member Status**
   - Member should lose access to organization resources
   - Member should revert to "INDIVIDUAL" status
   - Member's personal data (journal, etc.) should remain intact

### 🟩 7. Sidebar Navigation Testing
**Objective**: Test collapsible sidebar functionality

1. **Test Sidebar Collapse**
   - ✅ Click collapse button (←)
   - ✅ Sidebar should collapse to icons only
   - ✅ Hover over icons should show tooltips
   - ✅ State should persist on page refresh

2. **Test Sidebar Expand**
   - ✅ Click expand button (→)
   - ✅ Sidebar should expand to full width
   - ✅ Should show full navigation labels

### 🟩 8. Role-Based UI Testing
**Objective**: Verify UI adapts to user roles

1. **Individual User UI**
   - ✅ No organization-specific menu items
   - ✅ Shows "Join an Organization" CTA

2. **Organization Member UI**
   - ✅ Shows organization name
   - ✅ Shows member role
   - ✅ No admin-only menu items

3. **Organization Admin UI**
   - ✅ Shows all menu items including admin-only
   - ✅ Shows admin role
   - ✅ Can access all organization features

## Expected Results Summary

✅ **Individual users** can use personal recovery features without organization
✅ **Organization creation** automatically makes user an admin
✅ **Admin users** can invite/remove members and manage organization
✅ **Member users** can access organization resources but not admin features
✅ **Data isolation** ensures organizations cannot see each other's data
✅ **User removal** preserves personal data while removing organization access
✅ **Sidebar navigation** is collapsible and role-aware
✅ **JWT tokens** are updated when roles change

## Demo Accounts (from seed data)
- **Admin**: `<EMAIL>` / `admin123`
- **Staff**: `<EMAIL>` / `staff123`
- **Member**: `<EMAIL>` / `member123`
- **Individual**: `<EMAIL>` / `individual123`
